{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\training-frontend\\\\src\\\\components\\\\HomePage.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomeContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow: hidden;\n`;\n_c = HomeContainer;\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-light);\n  padding: var(--space-4) 0;\n\n  @media (max-width: 768px) {\n    padding: var(--space-3) 0;\n  }\n`;\nconst NavContainer = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--space-4);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n`;\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n\n  &::before {\n    content: '🤟';\n    font-size: 1.75rem;\n  }\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n\n    &::before {\n      font-size: 1.5rem;\n    }\n  }\n`;\nconst NavLinks = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-6);\n\n  @media (max-width: 768px) {\n    gap: var(--space-4);\n  }\n`;\nconst NavLink = styled.a`\n  color: var(--text-secondary);\n  text-decoration: none;\n  font-weight: 500;\n  font-size: 0.875rem;\n  transition: color 0.2s ease;\n\n  &:hover {\n    color: var(--primary-600);\n  }\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n  }\n`;\nconst HeroSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--space-20) var(--space-4) var(--space-16);\n  text-align: center;\n  position: relative;\n\n  @media (max-width: 768px) {\n    padding: var(--space-16) var(--space-4) var(--space-12);\n    min-height: calc(100vh - 80px);\n  }\n`;\nconst HeroContent = styled.div`\n  max-width: 800px;\n  width: 100%;\n  position: relative;\n  z-index: 2;\n`;\nconst Header = styled.div`\n  margin-bottom: 4rem;\n`;\n_c2 = Header;\nconst Title = styled.h1`\n  font-size: clamp(3rem, 8vw, 5rem);\n  margin-bottom: 1.5rem;\n  font-weight: 800;\n  background: linear-gradient(135deg, #FF6B9D 0%, #C44569 50%, #4ECDC4 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  line-height: 1.1;\n  letter-spacing: -0.02em;\n`;\n_c3 = Title;\nconst Subtitle = styled.h2`\n  font-size: clamp(1.2rem, 4vw, 1.8rem);\n  margin-bottom: 2rem;\n  color: rgba(255, 255, 255, 0.9);\n  font-weight: 400;\n  line-height: 1.4;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c4 = Subtitle;\nconst Description = styled.div`\n  max-width: 800px;\n  margin: 0 auto 4rem auto;\n  line-height: 1.8;\n  font-size: 1.1rem;\n  color: rgba(255, 255, 255, 0.85);\n  font-weight: 300;\n`;\n_c5 = Description;\nconst FeatureGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 2rem;\n  margin-bottom: 4rem;\n  max-width: 1200px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c6 = FeatureGrid;\nconst FeatureCard = styled.div`\n  background: rgba(255, 255, 255, 0.08);\n  backdrop-filter: blur(20px);\n  border-radius: 24px;\n  padding: 2.5rem 2rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 1px;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  }\n\n  &:hover {\n    transform: translateY(-8px);\n    background: rgba(255, 255, 255, 0.12);\n    border-color: rgba(255, 255, 255, 0.2);\n    box-shadow: \n      0 20px 40px rgba(0, 0, 0, 0.1),\n      0 0 0 1px rgba(255, 255, 255, 0.1);\n  }\n`;\n_c7 = FeatureCard;\nconst FeatureIcon = styled.div`\n  font-size: 3.5rem;\n  margin-bottom: 1.5rem;\n  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));\n`;\n_c8 = FeatureIcon;\nconst FeatureTitle = styled.h3`\n  font-size: 1.4rem;\n  margin-bottom: 1rem;\n  color: #4ECDC4;\n  font-weight: 600;\n  letter-spacing: -0.01em;\n`;\n_c9 = FeatureTitle;\nconst FeatureDescription = styled.p`\n  font-size: 1rem;\n  color: rgba(255, 255, 255, 0.8);\n  line-height: 1.6;\n  font-weight: 300;\n`;\n_c0 = FeatureDescription;\nconst CTAButton = styled.button`\n  background: linear-gradient(135deg, #FF6B9D 0%, #C44569 100%);\n  border: none;\n  border-radius: 50px;\n  padding: 1.2rem 3.5rem;\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: \n    0 10px 30px rgba(255, 107, 157, 0.3),\n    0 0 0 1px rgba(255, 255, 255, 0.1);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n    transition: left 0.5s;\n  }\n\n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: \n      0 20px 40px rgba(255, 107, 157, 0.4),\n      0 0 0 1px rgba(255, 255, 255, 0.2);\n\n    &::before {\n      left: 100%;\n    }\n  }\n\n  &:active {\n    transform: translateY(-1px);\n  }\n\n  @media (max-width: 768px) {\n    padding: 1rem 2.5rem;\n    font-size: 1.1rem;\n  }\n`;\n_c1 = CTAButton;\nconst FloatingElements = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  z-index: 1;\n`;\n_c10 = FloatingElements;\nconst FloatingElement = styled.div`\n  position: absolute;\n  width: 6px;\n  height: 6px;\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  animation: float 6s ease-in-out infinite;\n\n  &:nth-child(1) {\n    top: 20%;\n    left: 10%;\n    animation-delay: 0s;\n  }\n\n  &:nth-child(2) {\n    top: 60%;\n    right: 15%;\n    animation-delay: 2s;\n  }\n\n  &:nth-child(3) {\n    bottom: 30%;\n    left: 20%;\n    animation-delay: 4s;\n  }\n\n  @keyframes float {\n    0%, 100% { transform: translateY(0px); opacity: 0.3; }\n    50% { transform: translateY(-20px); opacity: 0.8; }\n  }\n`;\n_c11 = FloatingElement;\nconst HomePage = ({\n  onStartTraining\n}) => {\n  return /*#__PURE__*/_jsxDEV(HomeContainer, {\n    children: [/*#__PURE__*/_jsxDEV(FloatingElements, {\n      children: [/*#__PURE__*/_jsxDEV(FloatingElement, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FloatingElement, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FloatingElement, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Content, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          children: \"Sign Language Trainer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Subtitle, {\n          children: \"Master sign language with AI-powered practice sessions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Description, {\n        children: \"Transform your sign language learning journey with our innovative platform. Practice with real-time feedback while contributing to AI advancement. Every session helps improve detection algorithms for the deaf community.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FeatureGrid, {\n        children: [/*#__PURE__*/_jsxDEV(FeatureCard, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n            children: \"\\uD83D\\uDCF9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n            children: \"Real-time Practice\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n            children: \"Use your device's camera for interactive sign language practice with instant visual feedback\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n            children: \"\\uD83E\\uDD16\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n            children: \"AI-Powered Learning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n            children: \"Your practice sessions contribute to training advanced AI models for better sign detection\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n            children: \"\\uD83D\\uDCF1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n            children: \"Cross-Platform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n            children: \"Seamless experience across desktop and mobile devices with responsive design\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n            children: \"\\uD83D\\uDD12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n            children: \"Privacy First\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n            children: \"All recordings stored locally on your device with complete privacy control\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CTAButton, {\n        onClick: onStartTraining,\n        children: \"Start Your Journey \\u2192\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 296,\n    columnNumber: 5\n  }, this);\n};\n_c12 = HomePage;\nexport default HomePage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12;\n$RefreshReg$(_c, \"HomeContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"Subtitle\");\n$RefreshReg$(_c5, \"Description\");\n$RefreshReg$(_c6, \"FeatureGrid\");\n$RefreshReg$(_c7, \"FeatureCard\");\n$RefreshReg$(_c8, \"FeatureIcon\");\n$RefreshReg$(_c9, \"FeatureTitle\");\n$RefreshReg$(_c0, \"FeatureDescription\");\n$RefreshReg$(_c1, \"CTAButton\");\n$RefreshReg$(_c10, \"FloatingElements\");\n$RefreshReg$(_c11, \"FloatingElement\");\n$RefreshReg$(_c12, \"HomePage\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "HomeContainer", "div", "_c", "Navigation", "nav", "NavContainer", "Logo", "NavLinks", "NavLink", "a", "HeroSection", "section", "Hero<PERSON><PERSON><PERSON>", "Header", "_c2", "Title", "h1", "_c3", "Subtitle", "h2", "_c4", "Description", "_c5", "FeatureGrid", "_c6", "FeatureCard", "_c7", "FeatureIcon", "_c8", "FeatureTitle", "h3", "_c9", "FeatureDescription", "p", "_c0", "CTAButton", "button", "_c1", "FloatingElements", "_c10", "FloatingElement", "_c11", "HomePage", "onStartTraining", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Content", "onClick", "_c12", "$RefreshReg$"], "sources": ["D:/ASL/training-frontend/src/components/HomePage.js"], "sourcesContent": ["import React from 'react';\r\nimport styled from 'styled-components';\r\n\r\nconst HomeContainer = styled.div`\r\n  min-height: 100vh;\r\n  background: var(--bg-primary);\r\n  position: relative;\r\n  overflow: hidden;\r\n`;\r\n\r\nconst Navigation = styled.nav`\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 50;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border-bottom: 1px solid var(--border-light);\r\n  padding: var(--space-4) 0;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-3) 0;\r\n  }\r\n`;\r\n\r\nconst NavContainer = styled.div`\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--space-4);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n`;\r\n\r\nconst Logo = styled.div`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  color: var(--text-primary);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n\r\n  &::before {\r\n    content: '🤟';\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.25rem;\r\n\r\n    &::before {\r\n      font-size: 1.5rem;\r\n    }\r\n  }\r\n`;\r\n\r\nconst NavLinks = styled.div`\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-6);\r\n\r\n  @media (max-width: 768px) {\r\n    gap: var(--space-4);\r\n  }\r\n`;\r\n\r\nconst NavLink = styled.a`\r\n  color: var(--text-secondary);\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n  font-size: 0.875rem;\r\n  transition: color 0.2s ease;\r\n\r\n  &:hover {\r\n    color: var(--primary-600);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 0.8rem;\r\n  }\r\n`;\r\n\r\nconst HeroSection = styled.section`\r\n  min-height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: var(--space-20) var(--space-4) var(--space-16);\r\n  text-align: center;\r\n  position: relative;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-16) var(--space-4) var(--space-12);\r\n    min-height: calc(100vh - 80px);\r\n  }\r\n`;\r\n\r\nconst HeroContent = styled.div`\r\n  max-width: 800px;\r\n  width: 100%;\r\n  position: relative;\r\n  z-index: 2;\r\n`;\r\n\r\nconst Header = styled.div`\r\n  margin-bottom: 4rem;\r\n`;\r\n\r\nconst Title = styled.h1`\r\n  font-size: clamp(3rem, 8vw, 5rem);\r\n  margin-bottom: 1.5rem;\r\n  font-weight: 800;\r\n  background: linear-gradient(135deg, #FF6B9D 0%, #C44569 50%, #4ECDC4 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  line-height: 1.1;\r\n  letter-spacing: -0.02em;\r\n`;\r\n\r\nconst Subtitle = styled.h2`\r\n  font-size: clamp(1.2rem, 4vw, 1.8rem);\r\n  margin-bottom: 2rem;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-weight: 400;\r\n  line-height: 1.4;\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n`;\r\n\r\nconst Description = styled.div`\r\n  max-width: 800px;\r\n  margin: 0 auto 4rem auto;\r\n  line-height: 1.8;\r\n  font-size: 1.1rem;\r\n  color: rgba(255, 255, 255, 0.85);\r\n  font-weight: 300;\r\n`;\r\n\r\nconst FeatureGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\r\n  gap: 2rem;\r\n  margin-bottom: 4rem;\r\n  max-width: 1200px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n`;\r\n\r\nconst FeatureCard = styled.div`\r\n  background: rgba(255, 255, 255, 0.08);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 24px;\r\n  padding: 2.5rem 2rem;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 1px;\r\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n  }\r\n\r\n  &:hover {\r\n    transform: translateY(-8px);\r\n    background: rgba(255, 255, 255, 0.12);\r\n    border-color: rgba(255, 255, 255, 0.2);\r\n    box-shadow: \r\n      0 20px 40px rgba(0, 0, 0, 0.1),\r\n      0 0 0 1px rgba(255, 255, 255, 0.1);\r\n  }\r\n`;\r\n\r\nconst FeatureIcon = styled.div`\r\n  font-size: 3.5rem;\r\n  margin-bottom: 1.5rem;\r\n  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));\r\n`;\r\n\r\nconst FeatureTitle = styled.h3`\r\n  font-size: 1.4rem;\r\n  margin-bottom: 1rem;\r\n  color: #4ECDC4;\r\n  font-weight: 600;\r\n  letter-spacing: -0.01em;\r\n`;\r\n\r\nconst FeatureDescription = styled.p`\r\n  font-size: 1rem;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  line-height: 1.6;\r\n  font-weight: 300;\r\n`;\r\n\r\nconst CTAButton = styled.button`\r\n  background: linear-gradient(135deg, #FF6B9D 0%, #C44569 100%);\r\n  border: none;\r\n  border-radius: 50px;\r\n  padding: 1.2rem 3.5rem;\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n  color: white;\r\n  cursor: pointer;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  box-shadow: \r\n    0 10px 30px rgba(255, 107, 157, 0.3),\r\n    0 0 0 1px rgba(255, 255, 255, 0.1);\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: -100%;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n    transition: left 0.5s;\r\n  }\r\n\r\n  &:hover {\r\n    transform: translateY(-3px);\r\n    box-shadow: \r\n      0 20px 40px rgba(255, 107, 157, 0.4),\r\n      0 0 0 1px rgba(255, 255, 255, 0.2);\r\n\r\n    &::before {\r\n      left: 100%;\r\n    }\r\n  }\r\n\r\n  &:active {\r\n    transform: translateY(-1px);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: 1rem 2.5rem;\r\n    font-size: 1.1rem;\r\n  }\r\n`;\r\n\r\nconst FloatingElements = styled.div`\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  pointer-events: none;\r\n  z-index: 1;\r\n`;\r\n\r\nconst FloatingElement = styled.div`\r\n  position: absolute;\r\n  width: 6px;\r\n  height: 6px;\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-radius: 50%;\r\n  animation: float 6s ease-in-out infinite;\r\n\r\n  &:nth-child(1) {\r\n    top: 20%;\r\n    left: 10%;\r\n    animation-delay: 0s;\r\n  }\r\n\r\n  &:nth-child(2) {\r\n    top: 60%;\r\n    right: 15%;\r\n    animation-delay: 2s;\r\n  }\r\n\r\n  &:nth-child(3) {\r\n    bottom: 30%;\r\n    left: 20%;\r\n    animation-delay: 4s;\r\n  }\r\n\r\n  @keyframes float {\r\n    0%, 100% { transform: translateY(0px); opacity: 0.3; }\r\n    50% { transform: translateY(-20px); opacity: 0.8; }\r\n  }\r\n`;\r\n\r\nconst HomePage = ({ onStartTraining }) => {\r\n  return (\r\n    <HomeContainer>\r\n      <FloatingElements>\r\n        <FloatingElement />\r\n        <FloatingElement />\r\n        <FloatingElement />\r\n      </FloatingElements>\r\n      \r\n      <Content>\r\n        <Header>\r\n          <Title>Sign Language Trainer</Title>\r\n          <Subtitle>Master sign language with AI-powered practice sessions</Subtitle>\r\n        </Header>\r\n        \r\n        <Description>\r\n          Transform your sign language learning journey with our innovative platform. \r\n          Practice with real-time feedback while contributing to AI advancement. \r\n          Every session helps improve detection algorithms for the deaf community.\r\n        </Description>\r\n\r\n        <FeatureGrid>\r\n          <FeatureCard>\r\n            <FeatureIcon>📹</FeatureIcon>\r\n            <FeatureTitle>Real-time Practice</FeatureTitle>\r\n            <FeatureDescription>\r\n              Use your device's camera for interactive sign language practice with instant visual feedback\r\n            </FeatureDescription>\r\n          </FeatureCard>\r\n\r\n          <FeatureCard>\r\n            <FeatureIcon>🤖</FeatureIcon>\r\n            <FeatureTitle>AI-Powered Learning</FeatureTitle>\r\n            <FeatureDescription>\r\n              Your practice sessions contribute to training advanced AI models for better sign detection\r\n            </FeatureDescription>\r\n          </FeatureCard>\r\n\r\n          <FeatureCard>\r\n            <FeatureIcon>📱</FeatureIcon>\r\n            <FeatureTitle>Cross-Platform</FeatureTitle>\r\n            <FeatureDescription>\r\n              Seamless experience across desktop and mobile devices with responsive design\r\n            </FeatureDescription>\r\n          </FeatureCard>\r\n\r\n          <FeatureCard>\r\n            <FeatureIcon>🔒</FeatureIcon>\r\n            <FeatureTitle>Privacy First</FeatureTitle>\r\n            <FeatureDescription>\r\n              All recordings stored locally on your device with complete privacy control\r\n            </FeatureDescription>\r\n          </FeatureCard>\r\n        </FeatureGrid>\r\n\r\n        <CTAButton onClick={onStartTraining}>\r\n          Start Your Journey →\r\n        </CTAButton>\r\n      </Content>\r\n    </HomeContainer>\r\n  );\r\n};\r\n\r\nexport default HomePage; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,aAAa,GAAGH,MAAM,CAACI,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,aAAa;AAOnB,MAAMG,UAAU,GAAGN,MAAM,CAACO,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,YAAY,GAAGR,MAAM,CAACI,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMK,IAAI,GAAGT,MAAM,CAACI,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMM,QAAQ,GAAGV,MAAM,CAACI,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMO,OAAO,GAAGX,MAAM,CAACY,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,WAAW,GAAGb,MAAM,CAACc,OAAO;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,WAAW,GAAGf,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMY,MAAM,GAAGhB,MAAM,CAACI,GAAG;AACzB;AACA,CAAC;AAACa,GAAA,GAFID,MAAM;AAIZ,MAAME,KAAK,GAAGlB,MAAM,CAACmB,EAAE;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAVIF,KAAK;AAYX,MAAMG,QAAQ,GAAGrB,MAAM,CAACsB,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIF,QAAQ;AAWd,MAAMG,WAAW,GAAGxB,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GAPID,WAAW;AASjB,MAAME,WAAW,GAAG1B,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GARID,WAAW;AAUjB,MAAME,WAAW,GAAG5B,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GA5BID,WAAW;AA8BjB,MAAME,WAAW,GAAG9B,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAAC2B,GAAA,GAJID,WAAW;AAMjB,MAAME,YAAY,GAAGhC,MAAM,CAACiC,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIF,YAAY;AAQlB,MAAMG,kBAAkB,GAAGnC,MAAM,CAACoC,CAAC;AACnC;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,kBAAkB;AAOxB,MAAMG,SAAS,GAAGtC,MAAM,CAACuC,MAAM;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA9CIF,SAAS;AAgDf,MAAMG,gBAAgB,GAAGzC,MAAM,CAACI,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsC,IAAA,GARID,gBAAgB;AAUtB,MAAME,eAAe,GAAG3C,MAAM,CAACI,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwC,IAAA,GA9BID,eAAe;AAgCrB,MAAME,QAAQ,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EACxC,oBACE5C,OAAA,CAACC,aAAa;IAAA4C,QAAA,gBACZ7C,OAAA,CAACuC,gBAAgB;MAAAM,QAAA,gBACf7C,OAAA,CAACyC,eAAe;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnBjD,OAAA,CAACyC,eAAe;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnBjD,OAAA,CAACyC,eAAe;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEnBjD,OAAA,CAACkD,OAAO;MAAAL,QAAA,gBACN7C,OAAA,CAACc,MAAM;QAAA+B,QAAA,gBACL7C,OAAA,CAACgB,KAAK;UAAA6B,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpCjD,OAAA,CAACmB,QAAQ;UAAA0B,QAAA,EAAC;QAAsD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eAETjD,OAAA,CAACsB,WAAW;QAAAuB,QAAA,EAAC;MAIb;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAEdjD,OAAA,CAACwB,WAAW;QAAAqB,QAAA,gBACV7C,OAAA,CAAC0B,WAAW;UAAAmB,QAAA,gBACV7C,OAAA,CAAC4B,WAAW;YAAAiB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7BjD,OAAA,CAAC8B,YAAY;YAAAe,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAC/CjD,OAAA,CAACiC,kBAAkB;YAAAY,QAAA,EAAC;UAEpB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEdjD,OAAA,CAAC0B,WAAW;UAAAmB,QAAA,gBACV7C,OAAA,CAAC4B,WAAW;YAAAiB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7BjD,OAAA,CAAC8B,YAAY;YAAAe,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAChDjD,OAAA,CAACiC,kBAAkB;YAAAY,QAAA,EAAC;UAEpB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEdjD,OAAA,CAAC0B,WAAW;UAAAmB,QAAA,gBACV7C,OAAA,CAAC4B,WAAW;YAAAiB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7BjD,OAAA,CAAC8B,YAAY;YAAAe,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAC3CjD,OAAA,CAACiC,kBAAkB;YAAAY,QAAA,EAAC;UAEpB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEdjD,OAAA,CAAC0B,WAAW;UAAAmB,QAAA,gBACV7C,OAAA,CAAC4B,WAAW;YAAAiB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7BjD,OAAA,CAAC8B,YAAY;YAAAe,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAC1CjD,OAAA,CAACiC,kBAAkB;YAAAY,QAAA,EAAC;UAEpB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEdjD,OAAA,CAACoC,SAAS;QAACe,OAAO,EAAEP,eAAgB;QAAAC,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEpB,CAAC;AAACG,IAAA,GA7DIT,QAAQ;AA+Dd,eAAeA,QAAQ;AAAC,IAAAxC,EAAA,EAAAY,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAU,IAAA;AAAAC,YAAA,CAAAlD,EAAA;AAAAkD,YAAA,CAAAtC,GAAA;AAAAsC,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAxB,GAAA;AAAAwB,YAAA,CAAArB,GAAA;AAAAqB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAb,IAAA;AAAAa,YAAA,CAAAX,IAAA;AAAAW,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}