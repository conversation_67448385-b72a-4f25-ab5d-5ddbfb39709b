{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\training-frontend\\\\src\\\\components\\\\TrainingPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback } from 'react';\nimport styled from 'styled-components';\nimport Webcam from 'react-webcam';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrainingContainer = styled.div`\n  min-height: 100vh;\n  padding: 1.5rem;\n  color: white;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: \n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%),\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%);\n    pointer-events: none;\n  }\n\n  @media (max-width: 768px) {\n    padding: 1rem;\n  }\n`;\n_c = TrainingContainer;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  position: relative;\n  z-index: 2;\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    gap: 1rem;\n    text-align: center;\n  }\n`;\n_c2 = Header;\nconst BackButton = styled.button`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 50px;\n  padding: 0.8rem 1.5rem;\n  color: white;\n  cursor: pointer;\n  font-size: 0.95rem;\n  font-weight: 500;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.2);\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  }\n\n  @media (max-width: 768px) {\n    align-self: flex-start;\n  }\n`;\n_c3 = BackButton;\nconst Title = styled.h1`\n  font-size: clamp(2rem, 5vw, 3rem);\n  margin: 0;\n  font-weight: 700;\n  background: linear-gradient(135deg, #FF6B9D 0%, #4ECDC4 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  letter-spacing: -0.02em;\n`;\n_c4 = Title;\nconst MainContent = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 2;\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n`;\n_c5 = MainContent;\nconst CameraSection = styled.div`\n  background: rgba(255, 255, 255, 0.08);\n  backdrop-filter: blur(20px);\n  border-radius: 24px;\n  padding: 2rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n\n  &:hover {\n    border-color: rgba(255, 255, 255, 0.2);\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  }\n\n  @media (max-width: 768px) {\n    padding: 1.5rem;\n  }\n`;\n_c6 = CameraSection;\nconst CameraTitle = styled.h2`\n  font-size: 1.5rem;\n  margin-bottom: 1.5rem;\n  color: #4ECDC4;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\n_c7 = CameraTitle;\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: 16px;\n  overflow: hidden;\n  margin-bottom: 1.5rem;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n`;\n_c8 = WebcamContainer;\nconst StyledWebcam = styled(Webcam)`\n  width: 100%;\n  height: auto;\n  border-radius: 16px;\n  display: block;\n`;\n_c9 = StyledWebcam;\nconst RecordingOverlay = styled.div`\n  position: absolute;\n  top: 1rem;\n  right: 1rem;\n  background: ${props => props.isRecording ? 'linear-gradient(135deg, #FF6B9D, #C44569)' : 'rgba(255, 255, 255, 0.2)'};\n  backdrop-filter: blur(10px);\n  color: white;\n  padding: 0.6rem 1.2rem;\n  border-radius: 25px;\n  font-weight: 600;\n  font-size: 0.9rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\n  box-shadow: ${props => props.isRecording ? '0 8px 25px rgba(255, 107, 157, 0.4)' : '0 4px 15px rgba(0, 0, 0, 0.1)'};\n\n  @keyframes pulse {\n    0%, 100% { transform: scale(1); opacity: 1; }\n    50% { transform: scale(1.05); opacity: 0.8; }\n  }\n`;\n_c0 = RecordingOverlay;\nconst SignSection = styled.div`\n  background: rgba(255, 255, 255, 0.08);\n  backdrop-filter: blur(20px);\n  border-radius: 24px;\n  padding: 2rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    border-color: rgba(255, 255, 255, 0.2);\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  }\n\n  @media (max-width: 768px) {\n    padding: 1.5rem;\n  }\n`;\n_c1 = SignSection;\nconst SignTitle = styled.h2`\n  font-size: 1.5rem;\n  margin-bottom: 1.5rem;\n  color: #4ECDC4;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\n_c10 = SignTitle;\nconst SignDisplay = styled.div`\n  width: 280px;\n  height: 280px;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 6rem;\n  margin-bottom: 1.5rem;\n  border: 2px solid rgba(255, 255, 255, 0.1);\n  box-shadow: \n    inset 0 1px 0 rgba(255, 255, 255, 0.1),\n    0 10px 30px rgba(0, 0, 0, 0.2);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: scale(1.02);\n    border-color: rgba(255, 255, 255, 0.2);\n  }\n\n  @media (max-width: 768px) {\n    width: 220px;\n    height: 220px;\n    font-size: 4rem;\n  }\n`;\n_c11 = SignDisplay;\nconst SignName = styled.h3`\n  font-size: 1.8rem;\n  margin-bottom: 1rem;\n  color: #FF6B9D;\n  font-weight: 700;\n  letter-spacing: -0.01em;\n`;\n_c12 = SignName;\nconst SignDescription = styled.p`\n  text-align: center;\n  line-height: 1.7;\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 1rem;\n  font-weight: 400;\n  max-width: 300px;\n`;\n_c13 = SignDescription;\nconst ControlsSection = styled.div`\n  grid-column: 1 / -1;\n  display: flex;\n  justify-content: center;\n  gap: 1.5rem;\n  margin-top: 2rem;\n  position: relative;\n  z-index: 2;\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n    gap: 1rem;\n  }\n`;\n_c14 = ControlsSection;\nconst ControlButton = styled.button`\n  background: ${props => props.variant === 'primary' ? 'linear-gradient(135deg, #FF6B9D 0%, #C44569 100%)' : 'rgba(255, 255, 255, 0.1)'};\n  backdrop-filter: blur(10px);\n  border: 1px solid ${props => props.variant === 'primary' ? 'rgba(255, 107, 157, 0.3)' : 'rgba(255, 255, 255, 0.2)'};\n  border-radius: 50px;\n  padding: 1rem 2.5rem;\n  color: white;\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 600;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  min-width: 180px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  box-shadow: ${props => props.variant === 'primary' ? '0 10px 30px rgba(255, 107, 157, 0.3)' : '0 4px 15px rgba(0, 0, 0, 0.1)'};\n\n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: ${props => props.variant === 'primary' ? '0 15px 40px rgba(255, 107, 157, 0.4)' : '0 8px 25px rgba(0, 0, 0, 0.15)'};\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n\n  @media (max-width: 768px) {\n    width: 100%;\n    max-width: 300px;\n  }\n`;\n_c15 = ControlButton;\nconst StatusMessage = styled.div`\n  text-align: center;\n  margin-top: 1.5rem;\n  padding: 1rem 2rem;\n  border-radius: 16px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  color: ${props => props.type === 'success' ? '#4ECDC4' : props.type === 'error' ? '#FF6B9D' : 'rgba(255, 255, 255, 0.9)'};\n  font-weight: 500;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  position: relative;\n  z-index: 2;\n`;\n_c16 = StatusMessage;\nconst RecordingsSection = styled.div`\n  margin-top: 3rem;\n  text-align: center;\n  position: relative;\n  z-index: 2;\n`;\n_c17 = RecordingsSection;\nconst RecordingsTitle = styled.h3`\n  color: #4ECDC4;\n  margin-bottom: 1.5rem;\n  font-size: 1.5rem;\n  font-weight: 600;\n`;\n_c18 = RecordingsTitle;\nconst RecordingsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1.5rem;\n  max-width: 1000px;\n  margin: 0 auto;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n`;\n_c19 = RecordingsGrid;\nconst RecordingCard = styled.div`\n  background: rgba(255, 255, 255, 0.08);\n  backdrop-filter: blur(20px);\n  padding: 1.5rem;\n  border-radius: 16px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-4px);\n    border-color: rgba(255, 255, 255, 0.2);\n    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  }\n`;\n_c20 = RecordingCard;\nconst RecordingTitle = styled.p`\n  margin: 0 0 0.5rem 0;\n  color: #FF6B9D;\n  font-weight: 600;\n  font-size: 1.1rem;\n`;\n_c21 = RecordingTitle;\nconst RecordingTime = styled.p`\n  margin: 0 0 1rem 0;\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.7);\n`;\n_c22 = RecordingTime;\nconst DownloadButton = styled.button`\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  padding: 0.6rem 1.2rem;\n  color: white;\n  cursor: pointer;\n  font-size: 0.9rem;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin: 0 auto;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.2);\n    transform: translateY(-2px);\n  }\n`;\n\n// Sample sign language data\n_c23 = DownloadButton;\nconst signLanguageData = [{\n  name: \"Hello\",\n  emoji: \"👋\",\n  description: \"Wave your hand from side to side with your palm facing forward\"\n}, {\n  name: \"Thank You\",\n  emoji: \"🙏\",\n  description: \"Touch your chin with your fingertips and move your hand forward\"\n}, {\n  name: \"Yes\",\n  emoji: \"👍\",\n  description: \"Make a fist and nod it up and down\"\n}, {\n  name: \"No\",\n  emoji: \"👎\",\n  description: \"Make a fist and shake it from side to side\"\n}, {\n  name: \"Please\",\n  emoji: \"🤲\",\n  description: \"Place your open hand on your chest and move it in a circular motion\"\n}, {\n  name: \"Sorry\",\n  emoji: \"😔\",\n  description: \"Make a fist and rub it in a circular motion on your chest\"\n}];\nconst TrainingPage = ({\n  onBackToHome\n}) => {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [currentSign, setCurrentSign] = useState(0);\n  const [status, setStatus] = useState('');\n  const [recordedVideos, setRecordedVideos] = useState([]);\n  const webcamRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const recordedChunksRef = useRef([]);\n  const getRandomSign = useCallback(() => {\n    const randomIndex = Math.floor(Math.random() * signLanguageData.length);\n    setCurrentSign(randomIndex);\n  }, []);\n  const startRecording = useCallback(() => {\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n    mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, {\n      mimeType: 'video/webm'\n    });\n    mediaRecorderRef.current.ondataavailable = event => {\n      if (event.data.size > 0) {\n        recordedChunksRef.current.push(event.data);\n      }\n    };\n    mediaRecorderRef.current.onstop = () => {\n      const blob = new Blob(recordedChunksRef.current, {\n        type: 'video/webm'\n      });\n      const url = URL.createObjectURL(blob);\n      const timestamp = new Date().toISOString();\n      setRecordedVideos(prev => [...prev, {\n        id: timestamp,\n        url,\n        sign: signLanguageData[currentSign].name,\n        timestamp\n      }]);\n      recordedChunksRef.current = [];\n      setStatus('Recording saved successfully!');\n    };\n    mediaRecorderRef.current.start();\n    setIsRecording(true);\n    setStatus('Recording started...');\n  }, [currentSign]);\n  const stopRecording = useCallback(() => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  }, [isRecording]);\n  const downloadRecording = video => {\n    const a = document.createElement('a');\n    a.href = video.url;\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\n    a.click();\n  };\n  React.useEffect(() => {\n    getRandomSign();\n  }, [getRandomSign]);\n  return /*#__PURE__*/_jsxDEV(TrainingContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(BackButton, {\n        onClick: onBackToHome,\n        children: \"\\u2190 Back to Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Title, {\n        children: \"Practice Session\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 502,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(CameraSection, {\n        children: [/*#__PURE__*/_jsxDEV(CameraTitle, {\n          children: \"\\uD83D\\uDCF9 Your Camera\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(WebcamContainer, {\n          children: [/*#__PURE__*/_jsxDEV(StyledWebcam, {\n            ref: webcamRef,\n            audio: false,\n            screenshotFormat: \"image/jpeg\",\n            videoConstraints: {\n              width: 640,\n              height: 480,\n              facingMode: \"user\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(RecordingOverlay, {\n            isRecording: isRecording,\n            children: isRecording ? '🔴 Recording' : '📹 Ready'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 511,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SignSection, {\n        children: [/*#__PURE__*/_jsxDEV(SignTitle, {\n          children: \"\\uD83C\\uDFAF Practice This Sign\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SignDisplay, {\n          children: signLanguageData[currentSign].emoji\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SignName, {\n          children: signLanguageData[currentSign].name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SignDescription, {\n          children: signLanguageData[currentSign].description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 510,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ControlsSection, {\n      children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n        variant: \"secondary\",\n        onClick: getRandomSign,\n        disabled: isRecording,\n        children: \"\\uD83D\\uDD04 New Sign\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n        variant: \"primary\",\n        onClick: isRecording ? stopRecording : startRecording,\n        children: isRecording ? '⏹️ Stop Recording' : '🎬 Start Recording'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 7\n    }, this), status && /*#__PURE__*/_jsxDEV(StatusMessage, {\n      type: status.includes('error') ? 'error' : status.includes('success') ? 'success' : 'info',\n      children: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 9\n    }, this), recordedVideos.length > 0 && /*#__PURE__*/_jsxDEV(RecordingsSection, {\n      children: [/*#__PURE__*/_jsxDEV(RecordingsTitle, {\n        children: \"Your Practice Recordings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(RecordingsGrid, {\n        children: recordedVideos.map(video => /*#__PURE__*/_jsxDEV(RecordingCard, {\n          children: [/*#__PURE__*/_jsxDEV(RecordingTitle, {\n            children: video.sign\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(RecordingTime, {\n            children: new Date(video.timestamp).toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(DownloadButton, {\n            onClick: () => downloadRecording(video),\n            children: \"\\uD83D\\uDCE5 Download\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 17\n          }, this)]\n        }, video.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 570,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 501,\n    columnNumber: 5\n  }, this);\n};\n_s(TrainingPage, \"31VvV1/5i7G4yeUVu//5AOKbgnc=\");\n_c24 = TrainingPage;\nexport default TrainingPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24;\n$RefreshReg$(_c, \"TrainingContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"BackButton\");\n$RefreshReg$(_c4, \"Title\");\n$RefreshReg$(_c5, \"MainContent\");\n$RefreshReg$(_c6, \"CameraSection\");\n$RefreshReg$(_c7, \"CameraTitle\");\n$RefreshReg$(_c8, \"WebcamContainer\");\n$RefreshReg$(_c9, \"StyledWebcam\");\n$RefreshReg$(_c0, \"RecordingOverlay\");\n$RefreshReg$(_c1, \"SignSection\");\n$RefreshReg$(_c10, \"SignTitle\");\n$RefreshReg$(_c11, \"SignDisplay\");\n$RefreshReg$(_c12, \"SignName\");\n$RefreshReg$(_c13, \"SignDescription\");\n$RefreshReg$(_c14, \"ControlsSection\");\n$RefreshReg$(_c15, \"ControlButton\");\n$RefreshReg$(_c16, \"StatusMessage\");\n$RefreshReg$(_c17, \"RecordingsSection\");\n$RefreshReg$(_c18, \"RecordingsTitle\");\n$RefreshReg$(_c19, \"RecordingsGrid\");\n$RefreshReg$(_c20, \"RecordingCard\");\n$RefreshReg$(_c21, \"RecordingTitle\");\n$RefreshReg$(_c22, \"RecordingTime\");\n$RefreshReg$(_c23, \"DownloadButton\");\n$RefreshReg$(_c24, \"TrainingPage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "styled", "Webcam", "jsxDEV", "_jsxDEV", "TrainingContainer", "div", "_c", "Header", "_c2", "BackButton", "button", "_c3", "Title", "h1", "_c4", "MainContent", "_c5", "CameraSection", "_c6", "CameraTitle", "h2", "_c7", "WebcamContainer", "_c8", "StyledWebcam", "_c9", "RecordingOverlay", "props", "isRecording", "_c0", "SignSection", "_c1", "SignTitle", "_c10", "SignDisplay", "_c11", "SignName", "h3", "_c12", "SignDescription", "p", "_c13", "ControlsSection", "_c14", "ControlButton", "variant", "_c15", "StatusMessage", "type", "_c16", "RecordingsSection", "_c17", "RecordingsTitle", "_c18", "RecordingsGrid", "_c19", "RecordingCard", "_c20", "RecordingTitle", "_c21", "RecordingTime", "_c22", "DownloadButton", "_c23", "signLanguageData", "name", "emoji", "description", "TrainingPage", "onBackToHome", "_s", "setIsRecording", "currentSign", "setCurrentSign", "status", "setStatus", "recordedVideos", "setRecordedVideos", "webcamRef", "mediaRecorderRef", "recordedChunksRef", "getRandomSign", "randomIndex", "Math", "floor", "random", "length", "startRecording", "current", "MediaRecorder", "stream", "mimeType", "ondataavailable", "event", "data", "size", "push", "onstop", "blob", "Blob", "url", "URL", "createObjectURL", "timestamp", "Date", "toISOString", "prev", "id", "sign", "start", "stopRecording", "stop", "downloadRecording", "video", "a", "document", "createElement", "href", "download", "click", "useEffect", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "audio", "screenshotFormat", "videoConstraints", "width", "height", "facingMode", "disabled", "includes", "map", "toLocaleString", "_c24", "$RefreshReg$"], "sources": ["D:/ASL/training-frontend/src/components/TrainingPage.js"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\r\nimport styled from 'styled-components';\r\nimport Webcam from 'react-webcam';\r\n\r\nconst TrainingContainer = styled.div`\r\n  min-height: 100vh;\r\n  padding: 1.5rem;\r\n  color: white;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  position: relative;\r\n  overflow-x: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: \r\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%),\r\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%);\r\n    pointer-events: none;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: 1rem;\r\n  }\r\n`;\r\n\r\nconst Header = styled.div`\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2rem;\r\n  position: relative;\r\n  z-index: 2;\r\n\r\n  @media (max-width: 768px) {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n    text-align: center;\r\n  }\r\n`;\r\n\r\nconst BackButton = styled.button`\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 50px;\r\n  padding: 0.8rem 1.5rem;\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 0.95rem;\r\n  font-weight: 500;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n\r\n  &:hover {\r\n    background: rgba(255, 255, 255, 0.2);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    align-self: flex-start;\r\n  }\r\n`;\r\n\r\nconst Title = styled.h1`\r\n  font-size: clamp(2rem, 5vw, 3rem);\r\n  margin: 0;\r\n  font-weight: 700;\r\n  background: linear-gradient(135deg, #FF6B9D 0%, #4ECDC4 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  letter-spacing: -0.02em;\r\n`;\r\n\r\nconst MainContent = styled.div`\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 2rem;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  position: relative;\r\n  z-index: 2;\r\n\r\n  @media (max-width: 1024px) {\r\n    grid-template-columns: 1fr;\r\n    gap: 1.5rem;\r\n  }\r\n`;\r\n\r\nconst CameraSection = styled.div`\r\n  background: rgba(255, 255, 255, 0.08);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 24px;\r\n  padding: 2rem;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    border-color: rgba(255, 255, 255, 0.2);\r\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: 1.5rem;\r\n  }\r\n`;\r\n\r\nconst CameraTitle = styled.h2`\r\n  font-size: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n  color: #4ECDC4;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n`;\r\n\r\nconst WebcamContainer = styled.div`\r\n  position: relative;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  margin-bottom: 1.5rem;\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\r\n`;\r\n\r\nconst StyledWebcam = styled(Webcam)`\r\n  width: 100%;\r\n  height: auto;\r\n  border-radius: 16px;\r\n  display: block;\r\n`;\r\n\r\nconst RecordingOverlay = styled.div`\r\n  position: absolute;\r\n  top: 1rem;\r\n  right: 1rem;\r\n  background: ${props => props.isRecording \r\n    ? 'linear-gradient(135deg, #FF6B9D, #C44569)' \r\n    : 'rgba(255, 255, 255, 0.2)'};\r\n  backdrop-filter: blur(10px);\r\n  color: white;\r\n  padding: 0.6rem 1.2rem;\r\n  border-radius: 25px;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\r\n  box-shadow: ${props => props.isRecording \r\n    ? '0 8px 25px rgba(255, 107, 157, 0.4)' \r\n    : '0 4px 15px rgba(0, 0, 0, 0.1)'};\r\n\r\n  @keyframes pulse {\r\n    0%, 100% { transform: scale(1); opacity: 1; }\r\n    50% { transform: scale(1.05); opacity: 0.8; }\r\n  }\r\n`;\r\n\r\nconst SignSection = styled.div`\r\n  background: rgba(255, 255, 255, 0.08);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 24px;\r\n  padding: 2rem;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: center;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    border-color: rgba(255, 255, 255, 0.2);\r\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: 1.5rem;\r\n  }\r\n`;\r\n\r\nconst SignTitle = styled.h2`\r\n  font-size: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n  color: #4ECDC4;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n`;\r\n\r\nconst SignDisplay = styled.div`\r\n  width: 280px;\r\n  height: 280px;\r\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 6rem;\r\n  margin-bottom: 1.5rem;\r\n  border: 2px solid rgba(255, 255, 255, 0.1);\r\n  box-shadow: \r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1),\r\n    0 10px 30px rgba(0, 0, 0, 0.2);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.02);\r\n    border-color: rgba(255, 255, 255, 0.2);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 220px;\r\n    height: 220px;\r\n    font-size: 4rem;\r\n  }\r\n`;\r\n\r\nconst SignName = styled.h3`\r\n  font-size: 1.8rem;\r\n  margin-bottom: 1rem;\r\n  color: #FF6B9D;\r\n  font-weight: 700;\r\n  letter-spacing: -0.01em;\r\n`;\r\n\r\nconst SignDescription = styled.p`\r\n  text-align: center;\r\n  line-height: 1.7;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 1rem;\r\n  font-weight: 400;\r\n  max-width: 300px;\r\n`;\r\n\r\nconst ControlsSection = styled.div`\r\n  grid-column: 1 / -1;\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 1.5rem;\r\n  margin-top: 2rem;\r\n  position: relative;\r\n  z-index: 2;\r\n\r\n  @media (max-width: 768px) {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 1rem;\r\n  }\r\n`;\r\n\r\nconst ControlButton = styled.button`\r\n  background: ${props => props.variant === 'primary' \r\n    ? 'linear-gradient(135deg, #FF6B9D 0%, #C44569 100%)' \r\n    : 'rgba(255, 255, 255, 0.1)'};\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid ${props => props.variant === 'primary' \r\n    ? 'rgba(255, 107, 157, 0.3)' \r\n    : 'rgba(255, 255, 255, 0.2)'};\r\n  border-radius: 50px;\r\n  padding: 1rem 2.5rem;\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  min-width: 180px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  box-shadow: ${props => props.variant === 'primary' \r\n    ? '0 10px 30px rgba(255, 107, 157, 0.3)' \r\n    : '0 4px 15px rgba(0, 0, 0, 0.1)'};\r\n\r\n  &:hover {\r\n    transform: translateY(-3px);\r\n    box-shadow: ${props => props.variant === 'primary' \r\n      ? '0 15px 40px rgba(255, 107, 157, 0.4)' \r\n      : '0 8px 25px rgba(0, 0, 0, 0.15)'};\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 100%;\r\n    max-width: 300px;\r\n  }\r\n`;\r\n\r\nconst StatusMessage = styled.div`\r\n  text-align: center;\r\n  margin-top: 1.5rem;\r\n  padding: 1rem 2rem;\r\n  border-radius: 16px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  color: ${props => props.type === 'success' ? '#4ECDC4' : props.type === 'error' ? '#FF6B9D' : 'rgba(255, 255, 255, 0.9)'};\r\n  font-weight: 500;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  position: relative;\r\n  z-index: 2;\r\n`;\r\n\r\nconst RecordingsSection = styled.div`\r\n  margin-top: 3rem;\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 2;\r\n`;\r\n\r\nconst RecordingsTitle = styled.h3`\r\n  color: #4ECDC4;\r\n  margin-bottom: 1.5rem;\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n`;\r\n\r\nconst RecordingsGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 1.5rem;\r\n  max-width: 1000px;\r\n  margin: 0 auto;\r\n\r\n  @media (max-width: 768px) {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n  }\r\n`;\r\n\r\nconst RecordingCard = styled.div`\r\n  background: rgba(255, 255, 255, 0.08);\r\n  backdrop-filter: blur(20px);\r\n  padding: 1.5rem;\r\n  border-radius: 16px;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-4px);\r\n    border-color: rgba(255, 255, 255, 0.2);\r\n    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\r\n  }\r\n`;\r\n\r\nconst RecordingTitle = styled.p`\r\n  margin: 0 0 0.5rem 0;\r\n  color: #FF6B9D;\r\n  font-weight: 600;\r\n  font-size: 1.1rem;\r\n`;\r\n\r\nconst RecordingTime = styled.p`\r\n  margin: 0 0 1rem 0;\r\n  font-size: 0.9rem;\r\n  color: rgba(255, 255, 255, 0.7);\r\n`;\r\n\r\nconst DownloadButton = styled.button`\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 12px;\r\n  padding: 0.6rem 1.2rem;\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  margin: 0 auto;\r\n\r\n  &:hover {\r\n    background: rgba(255, 255, 255, 0.2);\r\n    transform: translateY(-2px);\r\n  }\r\n`;\r\n\r\n// Sample sign language data\r\nconst signLanguageData = [\r\n  {\r\n    name: \"Hello\",\r\n    emoji: \"👋\",\r\n    description: \"Wave your hand from side to side with your palm facing forward\"\r\n  },\r\n  {\r\n    name: \"Thank You\",\r\n    emoji: \"🙏\",\r\n    description: \"Touch your chin with your fingertips and move your hand forward\"\r\n  },\r\n  {\r\n    name: \"Yes\",\r\n    emoji: \"👍\",\r\n    description: \"Make a fist and nod it up and down\"\r\n  },\r\n  {\r\n    name: \"No\",\r\n    emoji: \"👎\",\r\n    description: \"Make a fist and shake it from side to side\"\r\n  },\r\n  {\r\n    name: \"Please\",\r\n    emoji: \"🤲\",\r\n    description: \"Place your open hand on your chest and move it in a circular motion\"\r\n  },\r\n  {\r\n    name: \"Sorry\",\r\n    emoji: \"😔\",\r\n    description: \"Make a fist and rub it in a circular motion on your chest\"\r\n  }\r\n];\r\n\r\nconst TrainingPage = ({ onBackToHome }) => {\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [currentSign, setCurrentSign] = useState(0);\r\n  const [status, setStatus] = useState('');\r\n  const [recordedVideos, setRecordedVideos] = useState([]);\r\n  const webcamRef = useRef(null);\r\n  const mediaRecorderRef = useRef(null);\r\n  const recordedChunksRef = useRef([]);\r\n\r\n  const getRandomSign = useCallback(() => {\r\n    const randomIndex = Math.floor(Math.random() * signLanguageData.length);\r\n    setCurrentSign(randomIndex);\r\n  }, []);\r\n\r\n  const startRecording = useCallback(() => {\r\n    if (!webcamRef.current) {\r\n      setStatus('Camera not available');\r\n      return;\r\n    }\r\n\r\n    mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, {\r\n      mimeType: 'video/webm'\r\n    });\r\n\r\n    mediaRecorderRef.current.ondataavailable = (event) => {\r\n      if (event.data.size > 0) {\r\n        recordedChunksRef.current.push(event.data);\r\n      }\r\n    };\r\n\r\n    mediaRecorderRef.current.onstop = () => {\r\n      const blob = new Blob(recordedChunksRef.current, {\r\n        type: 'video/webm'\r\n      });\r\n      const url = URL.createObjectURL(blob);\r\n      const timestamp = new Date().toISOString();\r\n      \r\n      setRecordedVideos(prev => [...prev, {\r\n        id: timestamp,\r\n        url,\r\n        sign: signLanguageData[currentSign].name,\r\n        timestamp\r\n      }]);\r\n      \r\n      recordedChunksRef.current = [];\r\n      setStatus('Recording saved successfully!');\r\n    };\r\n\r\n    mediaRecorderRef.current.start();\r\n    setIsRecording(true);\r\n    setStatus('Recording started...');\r\n  }, [currentSign]);\r\n\r\n  const stopRecording = useCallback(() => {\r\n    if (mediaRecorderRef.current && isRecording) {\r\n      mediaRecorderRef.current.stop();\r\n      setIsRecording(false);\r\n      setStatus('Processing recording...');\r\n    }\r\n  }, [isRecording]);\r\n\r\n  const downloadRecording = (video) => {\r\n    const a = document.createElement('a');\r\n    a.href = video.url;\r\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\r\n    a.click();\r\n  };\r\n\r\n  React.useEffect(() => {\r\n    getRandomSign();\r\n  }, [getRandomSign]);\r\n\r\n  return (\r\n    <TrainingContainer>\r\n      <Header>\r\n        <BackButton onClick={onBackToHome}>\r\n          ← Back to Home\r\n        </BackButton>\r\n        <Title>Practice Session</Title>\r\n        <div></div>\r\n      </Header>\r\n\r\n      <MainContent>\r\n        <CameraSection>\r\n          <CameraTitle>\r\n            📹 Your Camera\r\n          </CameraTitle>\r\n          <WebcamContainer>\r\n            <StyledWebcam\r\n              ref={webcamRef}\r\n              audio={false}\r\n              screenshotFormat=\"image/jpeg\"\r\n              videoConstraints={{\r\n                width: 640,\r\n                height: 480,\r\n                facingMode: \"user\"\r\n              }}\r\n            />\r\n            <RecordingOverlay isRecording={isRecording}>\r\n              {isRecording ? '🔴 Recording' : '📹 Ready'}\r\n            </RecordingOverlay>\r\n          </WebcamContainer>\r\n        </CameraSection>\r\n\r\n        <SignSection>\r\n          <SignTitle>\r\n            🎯 Practice This Sign\r\n          </SignTitle>\r\n          <SignDisplay>\r\n            {signLanguageData[currentSign].emoji}\r\n          </SignDisplay>\r\n          <SignName>{signLanguageData[currentSign].name}</SignName>\r\n          <SignDescription>\r\n            {signLanguageData[currentSign].description}\r\n          </SignDescription>\r\n        </SignSection>\r\n      </MainContent>\r\n\r\n      <ControlsSection>\r\n        <ControlButton \r\n          variant=\"secondary\" \r\n          onClick={getRandomSign}\r\n          disabled={isRecording}\r\n        >\r\n          🔄 New Sign\r\n        </ControlButton>\r\n        \r\n        <ControlButton \r\n          variant=\"primary\" \r\n          onClick={isRecording ? stopRecording : startRecording}\r\n        >\r\n          {isRecording ? '⏹️ Stop Recording' : '🎬 Start Recording'}\r\n        </ControlButton>\r\n      </ControlsSection>\r\n\r\n      {status && (\r\n        <StatusMessage type={status.includes('error') ? 'error' : status.includes('success') ? 'success' : 'info'}>\r\n          {status}\r\n        </StatusMessage>\r\n      )}\r\n\r\n      {recordedVideos.length > 0 && (\r\n        <RecordingsSection>\r\n          <RecordingsTitle>Your Practice Recordings</RecordingsTitle>\r\n          <RecordingsGrid>\r\n            {recordedVideos.map((video) => (\r\n              <RecordingCard key={video.id}>\r\n                <RecordingTitle>{video.sign}</RecordingTitle>\r\n                <RecordingTime>\r\n                  {new Date(video.timestamp).toLocaleString()}\r\n                </RecordingTime>\r\n                <DownloadButton onClick={() => downloadRecording(video)}>\r\n                  📥 Download\r\n                </DownloadButton>\r\n              </RecordingCard>\r\n            ))}\r\n          </RecordingsGrid>\r\n        </RecordingsSection>\r\n      )}\r\n    </TrainingContainer>\r\n  );\r\n};\r\n\r\nexport default TrainingPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,iBAAiB,GAAGJ,MAAM,CAACK,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAxBIF,iBAAiB;AA0BvB,MAAMG,MAAM,GAAGP,MAAM,CAACK,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAbID,MAAM;AAeZ,MAAME,UAAU,GAAGT,MAAM,CAACU,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAxBIF,UAAU;AA0BhB,MAAMG,KAAK,GAAGZ,MAAM,CAACa,EAAE;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIF,KAAK;AAWX,MAAMG,WAAW,GAAGf,MAAM,CAACK,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAbID,WAAW;AAejB,MAAME,aAAa,GAAGjB,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GAhBID,aAAa;AAkBnB,MAAME,WAAW,GAAGnB,MAAM,CAACoB,EAAE;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GARIF,WAAW;AAUjB,MAAMG,eAAe,GAAGtB,MAAM,CAACK,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GANID,eAAe;AAQrB,MAAME,YAAY,GAAGxB,MAAM,CAACC,MAAM,CAAC;AACnC;AACA;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GALID,YAAY;AAOlB,MAAME,gBAAgB,GAAG1B,MAAM,CAACK,GAAG;AACnC;AACA;AACA;AACA,gBAAgBsB,KAAK,IAAIA,KAAK,CAACC,WAAW,GACpC,2CAA2C,GAC3C,0BAA0B;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,qBAAqB,GAAG,MAAM;AAC1E,gBAAgBD,KAAK,IAAIA,KAAK,CAACC,WAAW,GACpC,qCAAqC,GACrC,+BAA+B;AACrC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAzBIH,gBAAgB;AA2BtB,MAAMI,WAAW,GAAG9B,MAAM,CAACK,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GApBID,WAAW;AAsBjB,MAAME,SAAS,GAAGhC,MAAM,CAACoB,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACa,IAAA,GARID,SAAS;AAUf,MAAME,WAAW,GAAGlC,MAAM,CAACK,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8B,IAAA,GA1BID,WAAW;AA4BjB,MAAME,QAAQ,GAAGpC,MAAM,CAACqC,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GANIF,QAAQ;AAQd,MAAMG,eAAe,GAAGvC,MAAM,CAACwC,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAPIF,eAAe;AASrB,MAAMG,eAAe,GAAG1C,MAAM,CAACK,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsC,IAAA,GAdID,eAAe;AAgBrB,MAAME,aAAa,GAAG5C,MAAM,CAACU,MAAM;AACnC,gBAAgBiB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,mDAAmD,GACnD,0BAA0B;AAChC;AACA,sBAAsBlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GACpD,0BAA0B,GAC1B,0BAA0B;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,sCAAsC,GACtC,+BAA+B;AACrC;AACA;AACA;AACA,kBAAkBlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,sCAAsC,GACtC,gCAAgC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAzCIF,aAAa;AA2CnB,MAAMG,aAAa,GAAG/C,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,WAAWsB,KAAK,IAAIA,KAAK,CAACqB,IAAI,KAAK,SAAS,GAAG,SAAS,GAAGrB,KAAK,CAACqB,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,0BAA0B;AAC1H;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAZIF,aAAa;AAcnB,MAAMG,iBAAiB,GAAGlD,MAAM,CAACK,GAAG;AACpC;AACA;AACA;AACA;AACA,CAAC;AAAC8C,IAAA,GALID,iBAAiB;AAOvB,MAAME,eAAe,GAAGpD,MAAM,CAACqC,EAAE;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACgB,IAAA,GALID,eAAe;AAOrB,MAAME,cAAc,GAAGtD,MAAM,CAACK,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkD,IAAA,GAXID,cAAc;AAapB,MAAME,aAAa,GAAGxD,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoD,IAAA,GAbID,aAAa;AAenB,MAAME,cAAc,GAAG1D,MAAM,CAACwC,CAAC;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACmB,IAAA,GALID,cAAc;AAOpB,MAAME,aAAa,GAAG5D,MAAM,CAACwC,CAAC;AAC9B;AACA;AACA;AACA,CAAC;AAACqB,IAAA,GAJID,aAAa;AAMnB,MAAME,cAAc,GAAG9D,MAAM,CAACU,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAqD,IAAA,GArBMD,cAAc;AAsBpB,MAAME,gBAAgB,GAAG,CACvB;EACEC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,KAAK;EACXC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAAC1C,WAAW,EAAE2C,cAAc,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC6E,MAAM,EAAEC,SAAS,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC+E,cAAc,EAAEC,iBAAiB,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAMiF,SAAS,GAAGhF,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMiF,gBAAgB,GAAGjF,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMkF,iBAAiB,GAAGlF,MAAM,CAAC,EAAE,CAAC;EAEpC,MAAMmF,aAAa,GAAGlF,WAAW,CAAC,MAAM;IACtC,MAAMmF,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGrB,gBAAgB,CAACsB,MAAM,CAAC;IACvEb,cAAc,CAACS,WAAW,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,cAAc,GAAGxF,WAAW,CAAC,MAAM;IACvC,IAAI,CAAC+E,SAAS,CAACU,OAAO,EAAE;MACtBb,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;IAEAI,gBAAgB,CAACS,OAAO,GAAG,IAAIC,aAAa,CAACX,SAAS,CAACU,OAAO,CAACE,MAAM,EAAE;MACrEC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEFZ,gBAAgB,CAACS,OAAO,CAACI,eAAe,GAAIC,KAAK,IAAK;MACpD,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;QACvBf,iBAAiB,CAACQ,OAAO,CAACQ,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;MAC5C;IACF,CAAC;IAEDf,gBAAgB,CAACS,OAAO,CAACS,MAAM,GAAG,MAAM;MACtC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACnB,iBAAiB,CAACQ,OAAO,EAAE;QAC/CxC,IAAI,EAAE;MACR,CAAC,CAAC;MACF,MAAMoD,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACrC,MAAMK,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAE1C5B,iBAAiB,CAAC6B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAClCC,EAAE,EAAEJ,SAAS;QACbH,GAAG;QACHQ,IAAI,EAAE5C,gBAAgB,CAACQ,WAAW,CAAC,CAACP,IAAI;QACxCsC;MACF,CAAC,CAAC,CAAC;MAEHvB,iBAAiB,CAACQ,OAAO,GAAG,EAAE;MAC9Bb,SAAS,CAAC,+BAA+B,CAAC;IAC5C,CAAC;IAEDI,gBAAgB,CAACS,OAAO,CAACqB,KAAK,CAAC,CAAC;IAChCtC,cAAc,CAAC,IAAI,CAAC;IACpBI,SAAS,CAAC,sBAAsB,CAAC;EACnC,CAAC,EAAE,CAACH,WAAW,CAAC,CAAC;EAEjB,MAAMsC,aAAa,GAAG/G,WAAW,CAAC,MAAM;IACtC,IAAIgF,gBAAgB,CAACS,OAAO,IAAI5D,WAAW,EAAE;MAC3CmD,gBAAgB,CAACS,OAAO,CAACuB,IAAI,CAAC,CAAC;MAC/BxC,cAAc,CAAC,KAAK,CAAC;MACrBI,SAAS,CAAC,yBAAyB,CAAC;IACtC;EACF,CAAC,EAAE,CAAC/C,WAAW,CAAC,CAAC;EAEjB,MAAMoF,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGJ,KAAK,CAACb,GAAG;IAClBc,CAAC,CAACI,QAAQ,GAAG,QAAQL,KAAK,CAACL,IAAI,IAAIK,KAAK,CAACV,SAAS,OAAO;IACzDW,CAAC,CAACK,KAAK,CAAC,CAAC;EACX,CAAC;EAED3H,KAAK,CAAC4H,SAAS,CAAC,MAAM;IACpBvC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,oBACE9E,OAAA,CAACC,iBAAiB;IAAAqH,QAAA,gBAChBtH,OAAA,CAACI,MAAM;MAAAkH,QAAA,gBACLtH,OAAA,CAACM,UAAU;QAACiH,OAAO,EAAErD,YAAa;QAAAoD,QAAA,EAAC;MAEnC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb3H,OAAA,CAACS,KAAK;QAAA6G,QAAA,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/B3H,OAAA;QAAAwH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAET3H,OAAA,CAACY,WAAW;MAAA0G,QAAA,gBACVtH,OAAA,CAACc,aAAa;QAAAwG,QAAA,gBACZtH,OAAA,CAACgB,WAAW;UAAAsG,QAAA,EAAC;QAEb;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACd3H,OAAA,CAACmB,eAAe;UAAAmG,QAAA,gBACdtH,OAAA,CAACqB,YAAY;YACXuG,GAAG,EAAEjD,SAAU;YACfkD,KAAK,EAAE,KAAM;YACbC,gBAAgB,EAAC,YAAY;YAC7BC,gBAAgB,EAAE;cAChBC,KAAK,EAAE,GAAG;cACVC,MAAM,EAAE,GAAG;cACXC,UAAU,EAAE;YACd;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF3H,OAAA,CAACuB,gBAAgB;YAACE,WAAW,EAAEA,WAAY;YAAA6F,QAAA,EACxC7F,WAAW,GAAG,cAAc,GAAG;UAAU;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEhB3H,OAAA,CAAC2B,WAAW;QAAA2F,QAAA,gBACVtH,OAAA,CAAC6B,SAAS;UAAAyF,QAAA,EAAC;QAEX;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZ3H,OAAA,CAAC+B,WAAW;UAAAuF,QAAA,EACTzD,gBAAgB,CAACQ,WAAW,CAAC,CAACN;QAAK;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACd3H,OAAA,CAACiC,QAAQ;UAAAqF,QAAA,EAAEzD,gBAAgB,CAACQ,WAAW,CAAC,CAACP;QAAI;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACzD3H,OAAA,CAACoC,eAAe;UAAAkF,QAAA,EACbzD,gBAAgB,CAACQ,WAAW,CAAC,CAACL;QAAW;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEd3H,OAAA,CAACuC,eAAe;MAAA+E,QAAA,gBACdtH,OAAA,CAACyC,aAAa;QACZC,OAAO,EAAC,WAAW;QACnB6E,OAAO,EAAEzC,aAAc;QACvBqD,QAAQ,EAAE1G,WAAY;QAAA6F,QAAA,EACvB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAEhB3H,OAAA,CAACyC,aAAa;QACZC,OAAO,EAAC,SAAS;QACjB6E,OAAO,EAAE9F,WAAW,GAAGkF,aAAa,GAAGvB,cAAe;QAAAkC,QAAA,EAErD7F,WAAW,GAAG,mBAAmB,GAAG;MAAoB;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEjBpD,MAAM,iBACLvE,OAAA,CAAC4C,aAAa;MAACC,IAAI,EAAE0B,MAAM,CAAC6D,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG7D,MAAM,CAAC6D,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,MAAO;MAAAd,QAAA,EACvG/C;IAAM;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAChB,EAEAlD,cAAc,CAACU,MAAM,GAAG,CAAC,iBACxBnF,OAAA,CAAC+C,iBAAiB;MAAAuE,QAAA,gBAChBtH,OAAA,CAACiD,eAAe;QAAAqE,QAAA,EAAC;MAAwB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC,eAC3D3H,OAAA,CAACmD,cAAc;QAAAmE,QAAA,EACZ7C,cAAc,CAAC4D,GAAG,CAAEvB,KAAK,iBACxB9G,OAAA,CAACqD,aAAa;UAAAiE,QAAA,gBACZtH,OAAA,CAACuD,cAAc;YAAA+D,QAAA,EAAER,KAAK,CAACL;UAAI;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC,eAC7C3H,OAAA,CAACyD,aAAa;YAAA6D,QAAA,EACX,IAAIjB,IAAI,CAACS,KAAK,CAACV,SAAS,CAAC,CAACkC,cAAc,CAAC;UAAC;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eAChB3H,OAAA,CAAC2D,cAAc;YAAC4D,OAAO,EAAEA,CAAA,KAAMV,iBAAiB,CAACC,KAAK,CAAE;YAAAQ,QAAA,EAAC;UAEzD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB,CAAC;QAAA,GAPCb,KAAK,CAACN,EAAE;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQb,CAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACpB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAExB,CAAC;AAACxD,EAAA,CAjKIF,YAAY;AAAAsE,IAAA,GAAZtE,YAAY;AAmKlB,eAAeA,YAAY;AAAC,IAAA9D,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAA2E,IAAA;AAAAC,YAAA,CAAArI,EAAA;AAAAqI,YAAA,CAAAnI,GAAA;AAAAmI,YAAA,CAAAhI,GAAA;AAAAgI,YAAA,CAAA7H,GAAA;AAAA6H,YAAA,CAAA3H,GAAA;AAAA2H,YAAA,CAAAzH,GAAA;AAAAyH,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAAlH,GAAA;AAAAkH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAA1G,IAAA;AAAA0G,YAAA,CAAAxG,IAAA;AAAAwG,YAAA,CAAArG,IAAA;AAAAqG,YAAA,CAAAlG,IAAA;AAAAkG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA7F,IAAA;AAAA6F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAxF,IAAA;AAAAwF,YAAA,CAAAtF,IAAA;AAAAsF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA5E,IAAA;AAAA4E,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}