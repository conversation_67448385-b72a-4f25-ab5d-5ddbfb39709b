{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\training-frontend\\\\src\\\\components\\\\HomePage.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomeContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow: hidden;\n`;\n_c = HomeContainer;\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-light);\n  padding: var(--space-4) 0;\n\n  @media (max-width: 768px) {\n    padding: var(--space-3) 0;\n  }\n`;\nconst NavContainer = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--space-4);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n`;\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n\n  &::before {\n    content: '🤟';\n    font-size: 1.75rem;\n  }\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n\n    &::before {\n      font-size: 1.5rem;\n    }\n  }\n`;\nconst NavLinks = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-6);\n\n  @media (max-width: 768px) {\n    gap: var(--space-4);\n  }\n`;\nconst NavLink = styled.a`\n  color: var(--text-secondary);\n  text-decoration: none;\n  font-weight: 500;\n  font-size: 0.875rem;\n  transition: color 0.2s ease;\n\n  &:hover {\n    color: var(--primary-600);\n  }\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n  }\n`;\nconst HeroSection = styled.section`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--space-20) var(--space-4) var(--space-16);\n  text-align: center;\n  position: relative;\n\n  @media (max-width: 768px) {\n    padding: var(--space-16) var(--space-4) var(--space-12);\n    min-height: calc(100vh - 80px);\n  }\n`;\nconst HeroContent = styled.div`\n  max-width: 800px;\n  width: 100%;\n  position: relative;\n  z-index: 2;\n`;\nconst HeroTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 3.75rem;\n  font-weight: 800;\n  color: var(--text-primary);\n  margin-bottom: var(--space-6);\n  line-height: 1.1;\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n    margin-bottom: var(--space-4);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 2rem;\n  }\n`;\nconst HeroSubtitle = styled.p`\n  font-size: 1.25rem;\n  font-weight: 400;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-8);\n  line-height: 1.6;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-6);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 1rem;\n  }\n`;\nconst HeroDescription = styled.p`\n  font-size: 1rem;\n  line-height: 1.7;\n  color: var(--text-tertiary);\n  margin-bottom: var(--space-10);\n  max-width: 500px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    font-size: 0.9rem;\n    margin-bottom: var(--space-8);\n    line-height: 1.6;\n  }\n`;\nconst HeroActions = styled.div`\n  display: flex;\n  gap: var(--space-4);\n  justify-content: center;\n  align-items: center;\n  flex-wrap: wrap;\n  margin-bottom: var(--space-12);\n\n  @media (max-width: 768px) {\n    gap: var(--space-3);\n    margin-bottom: var(--space-8);\n  }\n`;\nconst PrimaryButton = styled.button`\n  background: var(--primary-600);\n  color: white;\n  border: none;\n  padding: var(--space-4) var(--space-8);\n  border-radius: var(--radius-lg);\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-lg);\n\n  &:hover {\n    background: var(--primary-700);\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-xl);\n  }\n\n  &:active {\n    transform: translateY(0);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-3) var(--space-6);\n    font-size: 0.9rem;\n  }\n`;\nconst SecondaryButton = styled.button`\n  background: transparent;\n  color: var(--text-primary);\n  border: 2px solid var(--border-medium);\n  padding: var(--space-4) var(--space-8);\n  border-radius: var(--radius-lg);\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n\n  &:hover {\n    border-color: var(--primary-600);\n    color: var(--primary-600);\n    background: var(--primary-50);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-3) var(--space-6);\n    font-size: 0.9rem;\n  }\n`;\nconst FeaturesSection = styled.section`\n  padding: var(--space-20) var(--space-4);\n  background: var(--bg-secondary);\n\n  @media (max-width: 768px) {\n    padding: var(--space-16) var(--space-4);\n  }\n`;\nconst FeaturesContainer = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  text-align: center;\n`;\nconst SectionTitle = styled.h2`\n  font-family: var(--font-primary);\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin-bottom: var(--space-4);\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n`;\nconst SectionSubtitle = styled.p`\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-12);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-10);\n  }\n`;\nconst FeatureGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n  gap: var(--space-8);\n  margin-bottom: var(--space-16);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-6);\n  }\n`;\n_c2 = FeatureGrid;\nconst FeatureCard = styled.div`\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-light);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  text-align: center;\n  position: relative;\n  overflow: hidden;\n  box-shadow: var(--shadow-sm);\n\n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: var(--shadow-xl);\n    border-color: var(--primary-200);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n  }\n`;\n_c3 = FeatureCard;\nconst FeatureIcon = styled.div`\n  font-size: 3rem;\n  margin-bottom: var(--space-4);\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\n_c4 = FeatureIcon;\nconst FeatureTitle = styled.h3`\n  font-size: 1.25rem;\n  margin-bottom: var(--space-3);\n  color: var(--text-primary);\n  font-weight: 600;\n  font-family: var(--font-primary);\n`;\n_c5 = FeatureTitle;\nconst FeatureDescription = styled.p`\n  font-size: 0.9rem;\n  color: var(--text-secondary);\n  line-height: 1.6;\n  font-weight: 400;\n`;\n_c6 = FeatureDescription;\nconst HeroStats = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: var(--space-8);\n  margin-top: var(--space-8);\n\n  @media (max-width: 768px) {\n    gap: var(--space-6);\n    flex-wrap: wrap;\n  }\n`;\nconst StatItem = styled.div`\n  text-align: center;\n`;\nconst StatNumber = styled.div`\n  font-size: 2rem;\n  font-weight: 700;\n  color: var(--primary-600);\n  font-family: var(--font-primary);\n\n  @media (max-width: 768px) {\n    font-size: 1.5rem;\n  }\n`;\nconst StatLabel = styled.div`\n  font-size: 0.875rem;\n  color: var(--text-tertiary);\n  font-weight: 500;\n  margin-top: var(--space-1);\n`;\nconst HomePage = ({\n  onStartTraining\n}) => {\n  return /*#__PURE__*/_jsxDEV(HomeContainer, {\n    children: [/*#__PURE__*/_jsxDEV(FloatingElements, {\n      children: [/*#__PURE__*/_jsxDEV(FloatingElement, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FloatingElement, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FloatingElement, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Content, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          children: \"Sign Language Trainer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Subtitle, {\n          children: \"Master sign language with AI-powered practice sessions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Description, {\n        children: \"Transform your sign language learning journey with our innovative platform. Practice with real-time feedback while contributing to AI advancement. Every session helps improve detection algorithms for the deaf community.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FeatureGrid, {\n        children: [/*#__PURE__*/_jsxDEV(FeatureCard, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n            children: \"\\uD83D\\uDCF9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n            children: \"Real-time Practice\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n            children: \"Use your device's camera for interactive sign language practice with instant visual feedback\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n            children: \"\\uD83E\\uDD16\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n            children: \"AI-Powered Learning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n            children: \"Your practice sessions contribute to training advanced AI models for better sign detection\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n            children: \"\\uD83D\\uDCF1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n            children: \"Cross-Platform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n            children: \"Seamless experience across desktop and mobile devices with responsive design\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n            children: \"\\uD83D\\uDD12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureTitle, {\n            children: \"Privacy First\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureDescription, {\n            children: \"All recordings stored locally on your device with complete privacy control\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CTAButton, {\n        onClick: onStartTraining,\n        children: \"Start Your Journey \\u2192\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 367,\n    columnNumber: 5\n  }, this);\n};\n_c7 = HomePage;\nexport default HomePage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"HomeContainer\");\n$RefreshReg$(_c2, \"FeatureGrid\");\n$RefreshReg$(_c3, \"FeatureCard\");\n$RefreshReg$(_c4, \"FeatureIcon\");\n$RefreshReg$(_c5, \"FeatureTitle\");\n$RefreshReg$(_c6, \"FeatureDescription\");\n$RefreshReg$(_c7, \"HomePage\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "HomeContainer", "div", "_c", "Navigation", "nav", "NavContainer", "Logo", "NavLinks", "NavLink", "a", "HeroSection", "section", "Hero<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "h1", "HeroSubtitle", "p", "HeroDescription", "HeroActions", "PrimaryButton", "button", "SecondaryButton", "FeaturesSection", "FeaturesContainer", "SectionTitle", "h2", "SectionSubtitle", "FeatureGrid", "_c2", "FeatureCard", "_c3", "FeatureIcon", "_c4", "FeatureTitle", "h3", "_c5", "FeatureDescription", "_c6", "HeroStats", "StatItem", "StatNumber", "StatLabel", "HomePage", "onStartTraining", "children", "FloatingElements", "FloatingElement", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Content", "Header", "Title", "Subtitle", "Description", "CTAButton", "onClick", "_c7", "$RefreshReg$"], "sources": ["D:/ASL/training-frontend/src/components/HomePage.js"], "sourcesContent": ["import React from 'react';\r\nimport styled from 'styled-components';\r\n\r\nconst HomeContainer = styled.div`\r\n  min-height: 100vh;\r\n  background: var(--bg-primary);\r\n  position: relative;\r\n  overflow: hidden;\r\n`;\r\n\r\nconst Navigation = styled.nav`\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 50;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border-bottom: 1px solid var(--border-light);\r\n  padding: var(--space-4) 0;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-3) 0;\r\n  }\r\n`;\r\n\r\nconst NavContainer = styled.div`\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--space-4);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n`;\r\n\r\nconst Logo = styled.div`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  color: var(--text-primary);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n\r\n  &::before {\r\n    content: '🤟';\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.25rem;\r\n\r\n    &::before {\r\n      font-size: 1.5rem;\r\n    }\r\n  }\r\n`;\r\n\r\nconst NavLinks = styled.div`\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-6);\r\n\r\n  @media (max-width: 768px) {\r\n    gap: var(--space-4);\r\n  }\r\n`;\r\n\r\nconst NavLink = styled.a`\r\n  color: var(--text-secondary);\r\n  text-decoration: none;\r\n  font-weight: 500;\r\n  font-size: 0.875rem;\r\n  transition: color 0.2s ease;\r\n\r\n  &:hover {\r\n    color: var(--primary-600);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 0.8rem;\r\n  }\r\n`;\r\n\r\nconst HeroSection = styled.section`\r\n  min-height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: var(--space-20) var(--space-4) var(--space-16);\r\n  text-align: center;\r\n  position: relative;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-16) var(--space-4) var(--space-12);\r\n    min-height: calc(100vh - 80px);\r\n  }\r\n`;\r\n\r\nconst HeroContent = styled.div`\r\n  max-width: 800px;\r\n  width: 100%;\r\n  position: relative;\r\n  z-index: 2;\r\n`;\r\n\r\nconst HeroTitle = styled.h1`\r\n  font-family: var(--font-primary);\r\n  font-size: 3.75rem;\r\n  font-weight: 800;\r\n  color: var(--text-primary);\r\n  margin-bottom: var(--space-6);\r\n  line-height: 1.1;\r\n  letter-spacing: -0.02em;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 2.5rem;\r\n    margin-bottom: var(--space-4);\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    font-size: 2rem;\r\n  }\r\n`;\r\n\r\nconst HeroSubtitle = styled.p`\r\n  font-size: 1.25rem;\r\n  font-weight: 400;\r\n  color: var(--text-secondary);\r\n  margin-bottom: var(--space-8);\r\n  line-height: 1.6;\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.125rem;\r\n    margin-bottom: var(--space-6);\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    font-size: 1rem;\r\n  }\r\n`;\r\n\r\nconst HeroDescription = styled.p`\r\n  font-size: 1rem;\r\n  line-height: 1.7;\r\n  color: var(--text-tertiary);\r\n  margin-bottom: var(--space-10);\r\n  max-width: 500px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 0.9rem;\r\n    margin-bottom: var(--space-8);\r\n    line-height: 1.6;\r\n  }\r\n`;\r\n\r\nconst HeroActions = styled.div`\r\n  display: flex;\r\n  gap: var(--space-4);\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  margin-bottom: var(--space-12);\r\n\r\n  @media (max-width: 768px) {\r\n    gap: var(--space-3);\r\n    margin-bottom: var(--space-8);\r\n  }\r\n`;\r\n\r\nconst PrimaryButton = styled.button`\r\n  background: var(--primary-600);\r\n  color: white;\r\n  border: none;\r\n  padding: var(--space-4) var(--space-8);\r\n  border-radius: var(--radius-lg);\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  box-shadow: var(--shadow-lg);\r\n\r\n  &:hover {\r\n    background: var(--primary-700);\r\n    transform: translateY(-2px);\r\n    box-shadow: var(--shadow-xl);\r\n  }\r\n\r\n  &:active {\r\n    transform: translateY(0);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-3) var(--space-6);\r\n    font-size: 0.9rem;\r\n  }\r\n`;\r\n\r\nconst SecondaryButton = styled.button`\r\n  background: transparent;\r\n  color: var(--text-primary);\r\n  border: 2px solid var(--border-medium);\r\n  padding: var(--space-4) var(--space-8);\r\n  border-radius: var(--radius-lg);\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n\r\n  &:hover {\r\n    border-color: var(--primary-600);\r\n    color: var(--primary-600);\r\n    background: var(--primary-50);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-3) var(--space-6);\r\n    font-size: 0.9rem;\r\n  }\r\n`;\r\n\r\nconst FeaturesSection = styled.section`\r\n  padding: var(--space-20) var(--space-4);\r\n  background: var(--bg-secondary);\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-16) var(--space-4);\r\n  }\r\n`;\r\n\r\nconst FeaturesContainer = styled.div`\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  text-align: center;\r\n`;\r\n\r\nconst SectionTitle = styled.h2`\r\n  font-family: var(--font-primary);\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: var(--text-primary);\r\n  margin-bottom: var(--space-4);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 2rem;\r\n  }\r\n`;\r\n\r\nconst SectionSubtitle = styled.p`\r\n  font-size: 1.125rem;\r\n  color: var(--text-secondary);\r\n  margin-bottom: var(--space-12);\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n\r\n  @media (max-width: 768px) {\r\n    margin-bottom: var(--space-10);\r\n  }\r\n`;\r\n\r\nconst FeatureGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\r\n  gap: var(--space-8);\r\n  margin-bottom: var(--space-16);\r\n\r\n  @media (max-width: 768px) {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst FeatureCard = styled.div`\r\n  background: var(--bg-primary);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--space-8);\r\n  border: 1px solid var(--border-light);\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  text-align: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n  box-shadow: var(--shadow-sm);\r\n\r\n  &:hover {\r\n    transform: translateY(-4px);\r\n    box-shadow: var(--shadow-xl);\r\n    border-color: var(--primary-200);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst FeatureIcon = styled.div`\r\n  font-size: 3rem;\r\n  margin-bottom: var(--space-4);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 2.5rem;\r\n  }\r\n`;\r\n\r\nconst FeatureTitle = styled.h3`\r\n  font-size: 1.25rem;\r\n  margin-bottom: var(--space-3);\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  font-family: var(--font-primary);\r\n`;\r\n\r\nconst FeatureDescription = styled.p`\r\n  font-size: 0.9rem;\r\n  color: var(--text-secondary);\r\n  line-height: 1.6;\r\n  font-weight: 400;\r\n`;\r\n\r\nconst HeroStats = styled.div`\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: var(--space-8);\r\n  margin-top: var(--space-8);\r\n\r\n  @media (max-width: 768px) {\r\n    gap: var(--space-6);\r\n    flex-wrap: wrap;\r\n  }\r\n`;\r\n\r\nconst StatItem = styled.div`\r\n  text-align: center;\r\n`;\r\n\r\nconst StatNumber = styled.div`\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  color: var(--primary-600);\r\n  font-family: var(--font-primary);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.5rem;\r\n  }\r\n`;\r\n\r\nconst StatLabel = styled.div`\r\n  font-size: 0.875rem;\r\n  color: var(--text-tertiary);\r\n  font-weight: 500;\r\n  margin-top: var(--space-1);\r\n`;\r\n\r\nconst HomePage = ({ onStartTraining }) => {\r\n  return (\r\n    <HomeContainer>\r\n      <FloatingElements>\r\n        <FloatingElement />\r\n        <FloatingElement />\r\n        <FloatingElement />\r\n      </FloatingElements>\r\n      \r\n      <Content>\r\n        <Header>\r\n          <Title>Sign Language Trainer</Title>\r\n          <Subtitle>Master sign language with AI-powered practice sessions</Subtitle>\r\n        </Header>\r\n        \r\n        <Description>\r\n          Transform your sign language learning journey with our innovative platform. \r\n          Practice with real-time feedback while contributing to AI advancement. \r\n          Every session helps improve detection algorithms for the deaf community.\r\n        </Description>\r\n\r\n        <FeatureGrid>\r\n          <FeatureCard>\r\n            <FeatureIcon>📹</FeatureIcon>\r\n            <FeatureTitle>Real-time Practice</FeatureTitle>\r\n            <FeatureDescription>\r\n              Use your device's camera for interactive sign language practice with instant visual feedback\r\n            </FeatureDescription>\r\n          </FeatureCard>\r\n\r\n          <FeatureCard>\r\n            <FeatureIcon>🤖</FeatureIcon>\r\n            <FeatureTitle>AI-Powered Learning</FeatureTitle>\r\n            <FeatureDescription>\r\n              Your practice sessions contribute to training advanced AI models for better sign detection\r\n            </FeatureDescription>\r\n          </FeatureCard>\r\n\r\n          <FeatureCard>\r\n            <FeatureIcon>📱</FeatureIcon>\r\n            <FeatureTitle>Cross-Platform</FeatureTitle>\r\n            <FeatureDescription>\r\n              Seamless experience across desktop and mobile devices with responsive design\r\n            </FeatureDescription>\r\n          </FeatureCard>\r\n\r\n          <FeatureCard>\r\n            <FeatureIcon>🔒</FeatureIcon>\r\n            <FeatureTitle>Privacy First</FeatureTitle>\r\n            <FeatureDescription>\r\n              All recordings stored locally on your device with complete privacy control\r\n            </FeatureDescription>\r\n          </FeatureCard>\r\n        </FeatureGrid>\r\n\r\n        <CTAButton onClick={onStartTraining}>\r\n          Start Your Journey →\r\n        </CTAButton>\r\n      </Content>\r\n    </HomeContainer>\r\n  );\r\n};\r\n\r\nexport default HomePage; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,aAAa,GAAGH,MAAM,CAACI,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,aAAa;AAOnB,MAAMG,UAAU,GAAGN,MAAM,CAACO,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,YAAY,GAAGR,MAAM,CAACI,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMK,IAAI,GAAGT,MAAM,CAACI,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMM,QAAQ,GAAGV,MAAM,CAACI,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMO,OAAO,GAAGX,MAAM,CAACY,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,WAAW,GAAGb,MAAM,CAACc,OAAO;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,WAAW,GAAGf,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMY,SAAS,GAAGhB,MAAM,CAACiB,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,YAAY,GAAGlB,MAAM,CAACmB,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,eAAe,GAAGpB,MAAM,CAACmB,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAME,WAAW,GAAGrB,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMkB,aAAa,GAAGtB,MAAM,CAACuB,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,eAAe,GAAGxB,MAAM,CAACuB,MAAM;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAME,eAAe,GAAGzB,MAAM,CAACc,OAAO;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMY,iBAAiB,GAAG1B,MAAM,CAACI,GAAG;AACpC;AACA;AACA;AACA,CAAC;AAED,MAAMuB,YAAY,GAAG3B,MAAM,CAAC4B,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,eAAe,GAAG7B,MAAM,CAACmB,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMW,WAAW,GAAG9B,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2B,GAAA,GAVID,WAAW;AAYjB,MAAME,WAAW,GAAGhC,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6B,GAAA,GApBID,WAAW;AAsBjB,MAAME,WAAW,GAAGlC,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC+B,GAAA,GAPID,WAAW;AASjB,MAAME,YAAY,GAAGpC,MAAM,CAACqC,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIF,YAAY;AAQlB,MAAMG,kBAAkB,GAAGvC,MAAM,CAACmB,CAAC;AACnC;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GALID,kBAAkB;AAOxB,MAAME,SAAS,GAAGzC,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMsC,QAAQ,GAAG1C,MAAM,CAACI,GAAG;AAC3B;AACA,CAAC;AAED,MAAMuC,UAAU,GAAG3C,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMwC,SAAS,GAAG5C,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMyC,QAAQ,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EACxC,oBACE5C,OAAA,CAACC,aAAa;IAAA4C,QAAA,gBACZ7C,OAAA,CAAC8C,gBAAgB;MAAAD,QAAA,gBACf7C,OAAA,CAAC+C,eAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnBnD,OAAA,CAAC+C,eAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnBnD,OAAA,CAAC+C,eAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEnBnD,OAAA,CAACoD,OAAO;MAAAP,QAAA,gBACN7C,OAAA,CAACqD,MAAM;QAAAR,QAAA,gBACL7C,OAAA,CAACsD,KAAK;UAAAT,QAAA,EAAC;QAAqB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpCnD,OAAA,CAACuD,QAAQ;UAAAV,QAAA,EAAC;QAAsD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eAETnD,OAAA,CAACwD,WAAW;QAAAX,QAAA,EAAC;MAIb;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAEdnD,OAAA,CAAC4B,WAAW;QAAAiB,QAAA,gBACV7C,OAAA,CAAC8B,WAAW;UAAAe,QAAA,gBACV7C,OAAA,CAACgC,WAAW;YAAAa,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7BnD,OAAA,CAACkC,YAAY;YAAAW,QAAA,EAAC;UAAkB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAC/CnD,OAAA,CAACqC,kBAAkB;YAAAQ,QAAA,EAAC;UAEpB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEdnD,OAAA,CAAC8B,WAAW;UAAAe,QAAA,gBACV7C,OAAA,CAACgC,WAAW;YAAAa,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7BnD,OAAA,CAACkC,YAAY;YAAAW,QAAA,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAChDnD,OAAA,CAACqC,kBAAkB;YAAAQ,QAAA,EAAC;UAEpB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEdnD,OAAA,CAAC8B,WAAW;UAAAe,QAAA,gBACV7C,OAAA,CAACgC,WAAW;YAAAa,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7BnD,OAAA,CAACkC,YAAY;YAAAW,QAAA,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAC3CnD,OAAA,CAACqC,kBAAkB;YAAAQ,QAAA,EAAC;UAEpB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEdnD,OAAA,CAAC8B,WAAW;UAAAe,QAAA,gBACV7C,OAAA,CAACgC,WAAW;YAAAa,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7BnD,OAAA,CAACkC,YAAY;YAAAW,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAC1CnD,OAAA,CAACqC,kBAAkB;YAAAQ,QAAA,EAAC;UAEpB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEdnD,OAAA,CAACyD,SAAS;QAACC,OAAO,EAAEd,eAAgB;QAAAC,QAAA,EAAC;MAErC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEpB,CAAC;AAACQ,GAAA,GA7DIhB,QAAQ;AA+Dd,eAAeA,QAAQ;AAAC,IAAAxC,EAAA,EAAA0B,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAqB,GAAA;AAAAC,YAAA,CAAAzD,EAAA;AAAAyD,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAxB,GAAA;AAAAwB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}