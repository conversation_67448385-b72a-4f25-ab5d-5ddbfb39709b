import React from 'react';
import styled from 'styled-components';

const HomeContainer = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
  }
`;

const Content = styled.div`
  position: relative;
  z-index: 2;
  max-width: 1200px;
  width: 100%;
`;

const Header = styled.div`
  margin-bottom: 4rem;
`;

const Title = styled.h1`
  font-size: clamp(3rem, 8vw, 5rem);
  margin-bottom: 1.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #FF6B9D 0%, #C44569 50%, #4ECDC4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.1;
  letter-spacing: -0.02em;
`;

const Subtitle = styled.h2`
  font-size: clamp(1.2rem, 4vw, 1.8rem);
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  line-height: 1.4;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const Description = styled.div`
  max-width: 800px;
  margin: 0 auto 4rem auto;
  line-height: 1.8;
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.85);
  font-weight: 300;
`;

const FeatureGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
`;

const FeatureCard = styled.div`
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2.5rem 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  }

  &:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 
      0 20px 40px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }
`;

const FeatureIcon = styled.div`
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
`;

const FeatureTitle = styled.h3`
  font-size: 1.4rem;
  margin-bottom: 1rem;
  color: #4ECDC4;
  font-weight: 600;
  letter-spacing: -0.01em;
`;

const FeatureDescription = styled.p`
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  font-weight: 300;
`;

const CTAButton = styled.button`
  background: linear-gradient(135deg, #FF6B9D 0%, #C44569 100%);
  border: none;
  border-radius: 50px;
  padding: 1.2rem 3.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 10px 30px rgba(255, 107, 157, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 
      0 20px 40px rgba(255, 107, 157, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.2);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(-1px);
  }

  @media (max-width: 768px) {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
  }
`;

const FloatingElements = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
`;

const FloatingElement = styled.div`
  position: absolute;
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;

  &:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
  }

  &:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
  }

  &:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); opacity: 0.3; }
    50% { transform: translateY(-20px); opacity: 0.8; }
  }
`;

const HomePage = ({ onStartTraining }) => {
  return (
    <HomeContainer>
      <FloatingElements>
        <FloatingElement />
        <FloatingElement />
        <FloatingElement />
      </FloatingElements>
      
      <Content>
        <Header>
          <Title>Sign Language Trainer</Title>
          <Subtitle>Master sign language with AI-powered practice sessions</Subtitle>
        </Header>
        
        <Description>
          Transform your sign language learning journey with our innovative platform. 
          Practice with real-time feedback while contributing to AI advancement. 
          Every session helps improve detection algorithms for the deaf community.
        </Description>

        <FeatureGrid>
          <FeatureCard>
            <FeatureIcon>📹</FeatureIcon>
            <FeatureTitle>Real-time Practice</FeatureTitle>
            <FeatureDescription>
              Use your device's camera for interactive sign language practice with instant visual feedback
            </FeatureDescription>
          </FeatureCard>

          <FeatureCard>
            <FeatureIcon>🤖</FeatureIcon>
            <FeatureTitle>AI-Powered Learning</FeatureTitle>
            <FeatureDescription>
              Your practice sessions contribute to training advanced AI models for better sign detection
            </FeatureDescription>
          </FeatureCard>

          <FeatureCard>
            <FeatureIcon>📱</FeatureIcon>
            <FeatureTitle>Cross-Platform</FeatureTitle>
            <FeatureDescription>
              Seamless experience across desktop and mobile devices with responsive design
            </FeatureDescription>
          </FeatureCard>

          <FeatureCard>
            <FeatureIcon>🔒</FeatureIcon>
            <FeatureTitle>Privacy First</FeatureTitle>
            <FeatureDescription>
              All recordings stored locally on your device with complete privacy control
            </FeatureDescription>
          </FeatureCard>
        </FeatureGrid>

        <CTAButton onClick={onStartTraining}>
          Start Your Journey →
        </CTAButton>
      </Content>
    </HomeContainer>
  );
};

export default HomePage; 