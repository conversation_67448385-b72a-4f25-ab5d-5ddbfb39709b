import React from 'react';
import styled from 'styled-components';

const HomeContainer = styled.div`
  min-height: 100vh;
  background: var(--bg-primary);
  position: relative;
  overflow: hidden;
`;

const Navigation = styled.nav`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-light);
  padding: var(--space-4) 0;

  @media (max-width: 768px) {
    padding: var(--space-3) 0;
  }
`;

const NavContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const Logo = styled.div`
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-2);

  &::before {
    content: '🤟';
    font-size: 1.75rem;
  }

  @media (max-width: 768px) {
    font-size: 1.25rem;

    &::before {
      font-size: 1.5rem;
    }
  }
`;

const NavLinks = styled.div`
  display: flex;
  align-items: center;
  gap: var(--space-6);

  @media (max-width: 768px) {
    gap: var(--space-4);
  }
`;

const NavLink = styled.a`
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  transition: color 0.2s ease;

  &:hover {
    color: var(--primary-600);
  }

  @media (max-width: 768px) {
    font-size: 0.8rem;
  }
`;

const HeroSection = styled.section`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-20) var(--space-4) var(--space-16);
  text-align: center;
  position: relative;

  @media (max-width: 768px) {
    padding: var(--space-16) var(--space-4) var(--space-12);
    min-height: calc(100vh - 80px);
  }
`;

const HeroContent = styled.div`
  max-width: 800px;
  width: 100%;
  position: relative;
  z-index: 2;
`;

const HeroTitle = styled.h1`
  font-family: var(--font-primary);
  font-size: 3.75rem;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  line-height: 1.1;
  letter-spacing: -0.02em;

  @media (max-width: 768px) {
    font-size: 2.5rem;
    margin-bottom: var(--space-4);
  }

  @media (max-width: 480px) {
    font-size: 2rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.25rem;
  font-weight: 400;
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    font-size: 1.125rem;
    margin-bottom: var(--space-6);
  }

  @media (max-width: 480px) {
    font-size: 1rem;
  }
`;

const HeroDescription = styled.p`
  font-size: 1rem;
  line-height: 1.7;
  color: var(--text-tertiary);
  margin-bottom: var(--space-10);
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    font-size: 0.9rem;
    margin-bottom: var(--space-8);
    line-height: 1.6;
  }
`;

const HeroActions = styled.div`
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: var(--space-12);

  @media (max-width: 768px) {
    gap: var(--space-3);
    margin-bottom: var(--space-8);
  }
`;

const PrimaryButton = styled.button`
  background: var(--primary-600);
  color: white;
  border: none;
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  box-shadow: var(--shadow-lg);

  &:hover {
    background: var(--primary-700);
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
  }

  &:active {
    transform: translateY(0);
  }

  @media (max-width: 768px) {
    padding: var(--space-3) var(--space-6);
    font-size: 0.9rem;
  }
`;

const SecondaryButton = styled.button`
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--border-medium);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);

  &:hover {
    border-color: var(--primary-600);
    color: var(--primary-600);
    background: var(--primary-50);
  }

  @media (max-width: 768px) {
    padding: var(--space-3) var(--space-6);
    font-size: 0.9rem;
  }
`;

const FeaturesSection = styled.section`
  padding: var(--space-20) var(--space-4);
  background: var(--bg-secondary);

  @media (max-width: 768px) {
    padding: var(--space-16) var(--space-4);
  }
`;

const FeaturesContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
`;

const SectionTitle = styled.h2`
  font-family: var(--font-primary);
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const SectionSubtitle = styled.p`
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: var(--space-12);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    margin-bottom: var(--space-10);
  }
`;

const FeatureGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-16);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
`;

const FeatureCard = styled.div`
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  border: 1px solid var(--border-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
  }

  @media (max-width: 768px) {
    padding: var(--space-6);
  }
`;

const FeatureIcon = styled.div`
  font-size: 3rem;
  margin-bottom: var(--space-4);

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
`;

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  margin-bottom: var(--space-3);
  color: var(--text-primary);
  font-weight: 600;
  font-family: var(--font-primary);
`;

const FeatureDescription = styled.p`
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.6;
  font-weight: 400;
`;

const HeroStats = styled.div`
  display: flex;
  justify-content: center;
  gap: var(--space-8);
  margin-top: var(--space-8);

  @media (max-width: 768px) {
    gap: var(--space-6);
    flex-wrap: wrap;
  }
`;

const StatItem = styled.div`
  text-align: center;
`;

const StatNumber = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-600);
  font-family: var(--font-primary);

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: var(--text-tertiary);
  font-weight: 500;
  margin-top: var(--space-1);
`;

const HomePage = ({ onStartTraining }) => {
  return (
    <HomeContainer>
      <Navigation>
        <NavContainer>
          <Logo>ASL Trainer</Logo>
          <NavLinks>
            <NavLink href="#features">Features</NavLink>
            <NavLink href="#about">About</NavLink>
            <NavLink href="#contact">Contact</NavLink>
          </NavLinks>
        </NavContainer>
      </Navigation>

      <HeroSection>
        <HeroContent>
          <HeroTitle>Master Sign Language with AI</HeroTitle>
          <HeroSubtitle>
            Transform your learning journey with our innovative platform that combines
            real-time practice with AI-powered feedback
          </HeroSubtitle>
          <HeroDescription>
            Practice sign language using your camera while contributing to AI advancement.
            Every session helps improve detection algorithms for the deaf community.
          </HeroDescription>

          <HeroActions>
            <PrimaryButton onClick={onStartTraining}>
              🚀 Start Training
            </PrimaryButton>
            <SecondaryButton>
              📖 Learn More
            </SecondaryButton>
          </HeroActions>

          <HeroStats>
            <StatItem>
              <StatNumber>10K+</StatNumber>
              <StatLabel>Active Learners</StatLabel>
            </StatItem>
            <StatItem>
              <StatNumber>50+</StatNumber>
              <StatLabel>Sign Patterns</StatLabel>
            </StatItem>
            <StatItem>
              <StatNumber>95%</StatNumber>
              <StatLabel>Accuracy Rate</StatLabel>
            </StatItem>
          </HeroStats>
        </HeroContent>
      </HeroSection>

      <FeaturesSection id="features">
        <FeaturesContainer>
          <SectionTitle>Why Choose ASL Trainer?</SectionTitle>
          <SectionSubtitle>
            Experience the future of sign language learning with our cutting-edge features
          </SectionSubtitle>

          <FeatureGrid>
            <FeatureCard>
              <FeatureIcon>📹</FeatureIcon>
              <FeatureTitle>Real-time Practice</FeatureTitle>
              <FeatureDescription>
                Use your device's camera for interactive sign language practice with instant visual feedback and gesture recognition
              </FeatureDescription>
            </FeatureCard>

            <FeatureCard>
              <FeatureIcon>🤖</FeatureIcon>
              <FeatureTitle>AI-Powered Learning</FeatureTitle>
              <FeatureDescription>
                Advanced machine learning algorithms provide personalized feedback, track your progress, and adapt to your learning pace
              </FeatureDescription>
            </FeatureCard>

            <FeatureCard>
              <FeatureIcon>🌍</FeatureIcon>
              <FeatureTitle>Community Impact</FeatureTitle>
              <FeatureDescription>
                Your practice sessions contribute to improving AI models that benefit the entire deaf and hard-of-hearing community
              </FeatureDescription>
            </FeatureCard>

            <FeatureCard>
              <FeatureIcon>📱</FeatureIcon>
              <FeatureTitle>Cross-Platform</FeatureTitle>
              <FeatureDescription>
                Works seamlessly on desktop and mobile devices, allowing you to practice anywhere, anytime with responsive design
              </FeatureDescription>
            </FeatureCard>

            <FeatureCard>
              <FeatureIcon>🎯</FeatureIcon>
              <FeatureTitle>Personalized Learning</FeatureTitle>
              <FeatureDescription>
                Adaptive learning paths that adjust to your skill level and provide targeted exercises for continuous improvement
              </FeatureDescription>
            </FeatureCard>

            <FeatureCard>
              <FeatureIcon>🔒</FeatureIcon>
              <FeatureTitle>Privacy First</FeatureTitle>
              <FeatureDescription>
                Your recordings are processed locally and stored securely, ensuring your privacy while you learn and practice
              </FeatureDescription>
            </FeatureCard>
          </FeatureGrid>
        </FeaturesContainer>
      </FeaturesSection>
    </HomeContainer>
  );
};

export default HomePage; 