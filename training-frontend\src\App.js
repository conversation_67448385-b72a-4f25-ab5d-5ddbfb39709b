import React, { useState } from 'react';
import styled from 'styled-components';
import HomePage from './components/HomePage';
import TrainingPage from './components/TrainingPage';
import './App.css';

const AppContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-x: hidden;
`;

function App() {
  const [currentPage, setCurrentPage] = useState('home');

  const navigateToTraining = () => {
    setCurrentPage('training');
  };

  const navigateToHome = () => {
    setCurrentPage('home');
  };

  return (
    <AppContainer>
      {currentPage === 'home' ? (
        <HomePage onStartTraining={navigateToTraining} />
      ) : (
        <TrainingPage onBackToHome={navigateToHome} />
      )}
    </AppContainer>
  );
}

export default App;
