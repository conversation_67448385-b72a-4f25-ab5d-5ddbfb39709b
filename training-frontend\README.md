# Sign Language Trainer

A React-based web application for learning and practicing sign language with AI-powered training sessions. This app helps users learn sign language while contributing to AI model improvement through their practice recordings.

## Features

### 🏠 Welcome Home Page
- Beautiful, modern UI with gradient backgrounds
- Clear explanation of the app's purpose
- Feature highlights with animated cards
- Easy navigation to start training

### 📹 Camera Training
- Real-time camera access for both desktop and mobile
- Live video feed with recording capabilities
- Cross-platform compatibility (PC and mobile phones)
- Privacy-focused local recording storage

### 🎯 Interactive Sign Learning
- Random sign language prompts with emoji representations
- Detailed descriptions of how to perform each sign
- Easy sign switching with "New Sign" button
- Visual feedback during practice sessions

### 💾 Local Recording System
- Record your sign language practice sessions
- Automatic local storage of recordings
- Download functionality for saved recordings
- Organized recording history with timestamps

### 🎨 Modern UI/UX
- Responsive design for all screen sizes
- Smooth animations and transitions
- Glassmorphism design elements
- Accessible color schemes and typography

## Getting Started

### Prerequisites
- Node.js (version 14 or higher)
- npm or yarn package manager
- Modern web browser with camera access

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd training-frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

4. Open your browser and navigate to `http://localhost:3000`

### Building for Production

To create a production build:
```bash
npm run build
```

## How to Use

1. **Welcome Page**: Read about the app's features and click "Start Training"

2. **Camera Setup**: Allow camera access when prompted

3. **Practice Session**:
   - View the random sign language prompt on the right
   - Position yourself in front of the camera
   - Click "Start Recording" when ready
   - Perform the sign as shown
   - Click "Stop Recording" when finished

4. **Review Recordings**: 
   - View your saved recordings at the bottom
   - Download recordings for local storage
   - Practice with different signs using "New Sign"

## Technical Details

### Dependencies
- **React**: Frontend framework
- **react-webcam**: Camera access and video recording
- **styled-components**: CSS-in-JS styling
- **MediaRecorder API**: Video recording functionality

### Browser Compatibility
- Chrome 47+
- Firefox 25+
- Safari 14+
- Edge 79+

### Camera Requirements
- HTTPS connection (required for camera access)
- User permission for camera access
- Compatible webcam or mobile camera

## Privacy & Security

- All recordings are stored locally in the browser
- No data is sent to external servers
- Camera access requires explicit user permission
- Recordings can be downloaded and deleted locally

## Future Enhancements

- AI-powered sign detection and feedback
- Integration with machine learning models
- Progress tracking and statistics
- Social features for sharing progress
- More comprehensive sign language database
- Real-time sign recognition

## Contributing

This project is designed to collect training data for AI model improvement. Your practice sessions help create better sign language detection algorithms for the deaf community.

## License

This project is licensed under the MIT License.

## Support

For issues or questions, please open an issue in the repository or contact the development team.
