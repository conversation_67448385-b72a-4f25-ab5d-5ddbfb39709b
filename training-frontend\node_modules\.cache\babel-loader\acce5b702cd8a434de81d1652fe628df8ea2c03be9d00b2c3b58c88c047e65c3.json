{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\training-frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport HomePage from './components/HomePage';\nimport TrainingPage from './components/TrainingPage';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  font-family: var(--font-secondary);\n  overflow-x: hidden;\n  position: relative;\n\n  /* Subtle background pattern for visual interest */\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 25% 25%, var(--primary-50) 0%, transparent 50%),\n      radial-gradient(circle at 75% 75%, var(--secondary-50) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n_c = AppContainer;\nconst PageWrapper = styled.div`\n  position: relative;\n  z-index: 1;\n  min-height: 100vh;\n`;\n_c2 = PageWrapper;\nfunction App() {\n  _s();\n  const [currentPage, setCurrentPage] = useState('home');\n  const navigateToTraining = () => {\n    setCurrentPage('training');\n  };\n  const navigateToHome = () => {\n    setCurrentPage('home');\n  };\n  return /*#__PURE__*/_jsxDEV(AppContainer, {\n    children: /*#__PURE__*/_jsxDEV(PageWrapper, {\n      children: currentPage === 'home' ? /*#__PURE__*/_jsxDEV(HomePage, {\n        onStartTraining: navigateToTraining\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TrainingPage, {\n        onBackToHome: navigateToHome\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"KLlrbvIFn6o4dTsrFf/Szg7G3bM=\");\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"PageWrapper\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "useState", "styled", "HomePage", "TrainingPage", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "PageWrapper", "_c2", "App", "_s", "currentPage", "setCurrentPage", "navigateToTraining", "navigateToHome", "children", "onStartTraining", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onBackToHome", "_c3", "$RefreshReg$"], "sources": ["D:/ASL/training-frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport HomePage from './components/HomePage';\nimport TrainingPage from './components/TrainingPage';\nimport './App.css';\n\nconst AppContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  font-family: var(--font-secondary);\n  overflow-x: hidden;\n  position: relative;\n\n  /* Subtle background pattern for visual interest */\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 25% 25%, var(--primary-50) 0%, transparent 50%),\n      radial-gradient(circle at 75% 75%, var(--secondary-50) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n\nconst PageWrapper = styled.div`\n  position: relative;\n  z-index: 1;\n  min-height: 100vh;\n`;\n\nfunction App() {\n  const [currentPage, setCurrentPage] = useState('home');\n\n  const navigateToTraining = () => {\n    setCurrentPage('training');\n  };\n\n  const navigateToHome = () => {\n    setCurrentPage('home');\n  };\n\n  return (\n    <AppContainer>\n      <PageWrapper>\n        {currentPage === 'home' ? (\n          <HomePage onStartTraining={navigateToTraining} />\n        ) : (\n          <TrainingPage onBackToHome={navigateToHome} />\n        )}\n      </PageWrapper>\n    </AppContainer>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,YAAY,GAAGL,MAAM,CAACM,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GArBIF,YAAY;AAuBlB,MAAMG,WAAW,GAAGR,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAJID,WAAW;AAMjB,SAASE,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,MAAM,CAAC;EAEtD,MAAMe,kBAAkB,GAAGA,CAAA,KAAM;IAC/BD,cAAc,CAAC,UAAU,CAAC;EAC5B,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3BF,cAAc,CAAC,MAAM,CAAC;EACxB,CAAC;EAED,oBACET,OAAA,CAACC,YAAY;IAAAW,QAAA,eACXZ,OAAA,CAACI,WAAW;MAAAQ,QAAA,EACTJ,WAAW,KAAK,MAAM,gBACrBR,OAAA,CAACH,QAAQ;QAACgB,eAAe,EAAEH;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEjDjB,OAAA,CAACF,YAAY;QAACoB,YAAY,EAAEP;MAAe;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC9C;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB;AAACV,EAAA,CAtBQD,GAAG;AAAAa,GAAA,GAAHb,GAAG;AAwBZ,eAAeA,GAAG;AAAC,IAAAH,EAAA,EAAAE,GAAA,EAAAc,GAAA;AAAAC,YAAA,CAAAjB,EAAA;AAAAiB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}