{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\training-frontend\\\\src\\\\components\\\\TrainingPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback } from 'react';\nimport styled from 'styled-components';\nimport Webcam from 'react-webcam';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrainingContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n`;\n_c = TrainingContainer;\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-light);\n  padding: var(--space-4) 0;\n`;\nconst NavContainer = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--space-4);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n`;\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n\n  &::before {\n    content: '🤟';\n    font-size: 1.75rem;\n  }\n`;\nconst BackButton = styled.button`\n  background: transparent;\n  color: var(--text-secondary);\n  border: 1px solid var(--border-medium);\n  padding: var(--space-2) var(--space-4);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n\n  &:hover {\n    background: var(--gray-50);\n    color: var(--text-primary);\n    border-color: var(--border-dark);\n  }\n`;\n_c2 = BackButton;\nconst PageTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  text-align: center;\n  margin-bottom: var(--space-2);\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n`;\nconst PageSubtitle = styled.p`\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-12);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-10);\n  }\n`;\nconst TrainingGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-8);\n  max-width: 1200px;\n  margin: 0 auto var(--space-12);\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-6);\n  }\n`;\nconst CameraSection = styled.div`\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-lg);\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-xl);\n    border-color: var(--primary-200);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n  }\n`;\n_c3 = CameraSection;\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: var(--radius-xl);\n  overflow: hidden;\n  background: var(--gray-100);\n  aspect-ratio: 4/3;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid var(--border-light);\n  margin-bottom: var(--space-4);\n`;\n_c4 = WebcamContainer;\nconst StyledWebcam = styled(Webcam)`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n`;\n_c5 = StyledWebcam;\nconst RecordingOverlay = styled.div`\n  position: absolute;\n  top: var(--space-4);\n  right: var(--space-4);\n  background: ${props => props.isRecording ? 'var(--error-500)' : 'var(--gray-600)'};\n  color: white;\n  padding: var(--space-2) var(--space-4);\n  border-radius: var(--radius-full);\n  font-size: 0.875rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\n\n  @keyframes pulse {\n    0%, 100% { opacity: 1; }\n    50% { opacity: 0.7; }\n  }\n`;\n_c6 = RecordingOverlay;\nconst SignSection = styled.div`\n  background: rgba(255, 255, 255, 0.08);\n  backdrop-filter: blur(20px);\n  border-radius: 24px;\n  padding: 2rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    border-color: rgba(255, 255, 255, 0.2);\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  }\n\n  @media (max-width: 768px) {\n    padding: 1.5rem;\n  }\n`;\n_c7 = SignSection;\nconst SignTitle = styled.h2`\n  font-size: 1.5rem;\n  margin-bottom: 1.5rem;\n  color: #4ECDC4;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\n_c8 = SignTitle;\nconst SignDisplay = styled.div`\n  width: 280px;\n  height: 280px;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 6rem;\n  margin-bottom: 1.5rem;\n  border: 2px solid rgba(255, 255, 255, 0.1);\n  box-shadow: \n    inset 0 1px 0 rgba(255, 255, 255, 0.1),\n    0 10px 30px rgba(0, 0, 0, 0.2);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: scale(1.02);\n    border-color: rgba(255, 255, 255, 0.2);\n  }\n\n  @media (max-width: 768px) {\n    width: 220px;\n    height: 220px;\n    font-size: 4rem;\n  }\n`;\n_c9 = SignDisplay;\nconst SignName = styled.h3`\n  font-size: 1.8rem;\n  margin-bottom: 1rem;\n  color: #FF6B9D;\n  font-weight: 700;\n  letter-spacing: -0.01em;\n`;\n_c0 = SignName;\nconst SignDescription = styled.p`\n  text-align: center;\n  line-height: 1.7;\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 1rem;\n  font-weight: 400;\n  max-width: 300px;\n`;\n_c1 = SignDescription;\nconst ControlsSection = styled.div`\n  grid-column: 1 / -1;\n  display: flex;\n  justify-content: center;\n  gap: 1.5rem;\n  margin-top: 2rem;\n  position: relative;\n  z-index: 2;\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n    gap: 1rem;\n  }\n`;\n_c10 = ControlsSection;\nconst ControlButton = styled.button`\n  background: ${props => props.variant === 'primary' ? 'linear-gradient(135deg, #FF6B9D 0%, #C44569 100%)' : 'rgba(255, 255, 255, 0.1)'};\n  backdrop-filter: blur(10px);\n  border: 1px solid ${props => props.variant === 'primary' ? 'rgba(255, 107, 157, 0.3)' : 'rgba(255, 255, 255, 0.2)'};\n  border-radius: 50px;\n  padding: 1rem 2.5rem;\n  color: white;\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 600;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  min-width: 180px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  box-shadow: ${props => props.variant === 'primary' ? '0 10px 30px rgba(255, 107, 157, 0.3)' : '0 4px 15px rgba(0, 0, 0, 0.1)'};\n\n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: ${props => props.variant === 'primary' ? '0 15px 40px rgba(255, 107, 157, 0.4)' : '0 8px 25px rgba(0, 0, 0, 0.15)'};\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n\n  @media (max-width: 768px) {\n    width: 100%;\n    max-width: 300px;\n  }\n`;\n_c11 = ControlButton;\nconst StatusMessage = styled.div`\n  text-align: center;\n  margin-top: 1.5rem;\n  padding: 1rem 2rem;\n  border-radius: 16px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  color: ${props => props.type === 'success' ? '#4ECDC4' : props.type === 'error' ? '#FF6B9D' : 'rgba(255, 255, 255, 0.9)'};\n  font-weight: 500;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  position: relative;\n  z-index: 2;\n`;\n_c12 = StatusMessage;\nconst RecordingsSection = styled.div`\n  margin-top: 3rem;\n  text-align: center;\n  position: relative;\n  z-index: 2;\n`;\n_c13 = RecordingsSection;\nconst RecordingsTitle = styled.h3`\n  color: #4ECDC4;\n  margin-bottom: 1.5rem;\n  font-size: 1.5rem;\n  font-weight: 600;\n`;\n_c14 = RecordingsTitle;\nconst RecordingsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1.5rem;\n  max-width: 1000px;\n  margin: 0 auto;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n`;\n_c15 = RecordingsGrid;\nconst RecordingCard = styled.div`\n  background: rgba(255, 255, 255, 0.08);\n  backdrop-filter: blur(20px);\n  padding: 1.5rem;\n  border-radius: 16px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-4px);\n    border-color: rgba(255, 255, 255, 0.2);\n    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  }\n`;\n_c16 = RecordingCard;\nconst RecordingTitle = styled.p`\n  margin: 0 0 0.5rem 0;\n  color: #FF6B9D;\n  font-weight: 600;\n  font-size: 1.1rem;\n`;\n_c17 = RecordingTitle;\nconst RecordingTime = styled.p`\n  margin: 0 0 1rem 0;\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.7);\n`;\n_c18 = RecordingTime;\nconst DownloadButton = styled.button`\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  padding: 0.6rem 1.2rem;\n  color: white;\n  cursor: pointer;\n  font-size: 0.9rem;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin: 0 auto;\n\n  &:hover {\n    background: rgba(255, 255, 255, 0.2);\n    transform: translateY(-2px);\n  }\n`;\n\n// Sample sign language data\n_c19 = DownloadButton;\nconst signLanguageData = [{\n  name: \"Hello\",\n  emoji: \"👋\",\n  description: \"Wave your hand from side to side with your palm facing forward\"\n}, {\n  name: \"Thank You\",\n  emoji: \"🙏\",\n  description: \"Touch your chin with your fingertips and move your hand forward\"\n}, {\n  name: \"Yes\",\n  emoji: \"👍\",\n  description: \"Make a fist and nod it up and down\"\n}, {\n  name: \"No\",\n  emoji: \"👎\",\n  description: \"Make a fist and shake it from side to side\"\n}, {\n  name: \"Please\",\n  emoji: \"🤲\",\n  description: \"Place your open hand on your chest and move it in a circular motion\"\n}, {\n  name: \"Sorry\",\n  emoji: \"😔\",\n  description: \"Make a fist and rub it in a circular motion on your chest\"\n}];\nconst TrainingPage = ({\n  onBackToHome\n}) => {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [currentSign, setCurrentSign] = useState(0);\n  const [status, setStatus] = useState('');\n  const [recordedVideos, setRecordedVideos] = useState([]);\n  const webcamRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const recordedChunksRef = useRef([]);\n  const getRandomSign = useCallback(() => {\n    const randomIndex = Math.floor(Math.random() * signLanguageData.length);\n    setCurrentSign(randomIndex);\n  }, []);\n  const startRecording = useCallback(() => {\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n    mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, {\n      mimeType: 'video/webm'\n    });\n    mediaRecorderRef.current.ondataavailable = event => {\n      if (event.data.size > 0) {\n        recordedChunksRef.current.push(event.data);\n      }\n    };\n    mediaRecorderRef.current.onstop = () => {\n      const blob = new Blob(recordedChunksRef.current, {\n        type: 'video/webm'\n      });\n      const url = URL.createObjectURL(blob);\n      const timestamp = new Date().toISOString();\n      setRecordedVideos(prev => [...prev, {\n        id: timestamp,\n        url,\n        sign: signLanguageData[currentSign].name,\n        timestamp\n      }]);\n      recordedChunksRef.current = [];\n      setStatus('Recording saved successfully!');\n    };\n    mediaRecorderRef.current.start();\n    setIsRecording(true);\n    setStatus('Recording started...');\n  }, [currentSign]);\n  const stopRecording = useCallback(() => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  }, [isRecording]);\n  const downloadRecording = video => {\n    const a = document.createElement('a');\n    a.href = video.url;\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\n    a.click();\n  };\n  React.useEffect(() => {\n    getRandomSign();\n  }, [getRandomSign]);\n  return /*#__PURE__*/_jsxDEV(TrainingContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(BackButton, {\n        onClick: onBackToHome,\n        children: \"\\u2190 Back to Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Title, {\n        children: \"Practice Session\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(CameraSection, {\n        children: [/*#__PURE__*/_jsxDEV(CameraTitle, {\n          children: \"\\uD83D\\uDCF9 Your Camera\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(WebcamContainer, {\n          children: [/*#__PURE__*/_jsxDEV(StyledWebcam, {\n            ref: webcamRef,\n            audio: false,\n            screenshotFormat: \"image/jpeg\",\n            videoConstraints: {\n              width: 640,\n              height: 480,\n              facingMode: \"user\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(RecordingOverlay, {\n            isRecording: isRecording,\n            children: isRecording ? '🔴 Recording' : '📹 Ready'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SignSection, {\n        children: [/*#__PURE__*/_jsxDEV(SignTitle, {\n          children: \"\\uD83C\\uDFAF Practice This Sign\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SignDisplay, {\n          children: signLanguageData[currentSign].emoji\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SignName, {\n          children: signLanguageData[currentSign].name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SignDescription, {\n          children: signLanguageData[currentSign].description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ControlsSection, {\n      children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n        variant: \"secondary\",\n        onClick: getRandomSign,\n        disabled: isRecording,\n        children: \"\\uD83D\\uDD04 New Sign\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n        variant: \"primary\",\n        onClick: isRecording ? stopRecording : startRecording,\n        children: isRecording ? '⏹️ Stop Recording' : '🎬 Start Recording'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 548,\n      columnNumber: 7\n    }, this), status && /*#__PURE__*/_jsxDEV(StatusMessage, {\n      type: status.includes('error') ? 'error' : status.includes('success') ? 'success' : 'info',\n      children: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 566,\n      columnNumber: 9\n    }, this), recordedVideos.length > 0 && /*#__PURE__*/_jsxDEV(RecordingsSection, {\n      children: [/*#__PURE__*/_jsxDEV(RecordingsTitle, {\n        children: \"Your Practice Recordings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(RecordingsGrid, {\n        children: recordedVideos.map(video => /*#__PURE__*/_jsxDEV(RecordingCard, {\n          children: [/*#__PURE__*/_jsxDEV(RecordingTitle, {\n            children: video.sign\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(RecordingTime, {\n            children: new Date(video.timestamp).toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(DownloadButton, {\n            onClick: () => downloadRecording(video),\n            children: \"\\uD83D\\uDCE5 Download\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 17\n          }, this)]\n        }, video.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 503,\n    columnNumber: 5\n  }, this);\n};\n_s(TrainingPage, \"31VvV1/5i7G4yeUVu//5AOKbgnc=\");\n_c20 = TrainingPage;\nexport default TrainingPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20;\n$RefreshReg$(_c, \"TrainingContainer\");\n$RefreshReg$(_c2, \"BackButton\");\n$RefreshReg$(_c3, \"CameraSection\");\n$RefreshReg$(_c4, \"WebcamContainer\");\n$RefreshReg$(_c5, \"StyledWebcam\");\n$RefreshReg$(_c6, \"RecordingOverlay\");\n$RefreshReg$(_c7, \"SignSection\");\n$RefreshReg$(_c8, \"SignTitle\");\n$RefreshReg$(_c9, \"SignDisplay\");\n$RefreshReg$(_c0, \"SignName\");\n$RefreshReg$(_c1, \"SignDescription\");\n$RefreshReg$(_c10, \"ControlsSection\");\n$RefreshReg$(_c11, \"ControlButton\");\n$RefreshReg$(_c12, \"StatusMessage\");\n$RefreshReg$(_c13, \"RecordingsSection\");\n$RefreshReg$(_c14, \"RecordingsTitle\");\n$RefreshReg$(_c15, \"RecordingsGrid\");\n$RefreshReg$(_c16, \"RecordingCard\");\n$RefreshReg$(_c17, \"RecordingTitle\");\n$RefreshReg$(_c18, \"RecordingTime\");\n$RefreshReg$(_c19, \"DownloadButton\");\n$RefreshReg$(_c20, \"TrainingPage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "styled", "Webcam", "jsxDEV", "_jsxDEV", "TrainingContainer", "div", "_c", "Navigation", "nav", "NavContainer", "Logo", "BackButton", "button", "_c2", "Page<PERSON><PERSON>le", "h1", "PageSubtitle", "p", "TrainingGrid", "CameraSection", "_c3", "WebcamContainer", "_c4", "StyledWebcam", "_c5", "RecordingOverlay", "props", "isRecording", "_c6", "SignSection", "_c7", "SignTitle", "h2", "_c8", "SignDisplay", "_c9", "SignName", "h3", "_c0", "SignDescription", "_c1", "ControlsSection", "_c10", "ControlButton", "variant", "_c11", "StatusMessage", "type", "_c12", "RecordingsSection", "_c13", "RecordingsTitle", "_c14", "RecordingsGrid", "_c15", "RecordingCard", "_c16", "RecordingTitle", "_c17", "RecordingTime", "_c18", "DownloadButton", "_c19", "signLanguageData", "name", "emoji", "description", "TrainingPage", "onBackToHome", "_s", "setIsRecording", "currentSign", "setCurrentSign", "status", "setStatus", "recordedVideos", "setRecordedVideos", "webcamRef", "mediaRecorderRef", "recordedChunksRef", "getRandomSign", "randomIndex", "Math", "floor", "random", "length", "startRecording", "current", "MediaRecorder", "stream", "mimeType", "ondataavailable", "event", "data", "size", "push", "onstop", "blob", "Blob", "url", "URL", "createObjectURL", "timestamp", "Date", "toISOString", "prev", "id", "sign", "start", "stopRecording", "stop", "downloadRecording", "video", "a", "document", "createElement", "href", "download", "click", "useEffect", "children", "Header", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Title", "MainContent", "CameraTitle", "ref", "audio", "screenshotFormat", "videoConstraints", "width", "height", "facingMode", "disabled", "includes", "map", "toLocaleString", "_c20", "$RefreshReg$"], "sources": ["D:/ASL/training-frontend/src/components/TrainingPage.js"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\r\nimport styled from 'styled-components';\r\nimport Webcam from 'react-webcam';\r\n\r\nconst TrainingContainer = styled.div`\r\n  min-height: 100vh;\r\n  background: var(--bg-primary);\r\n  position: relative;\r\n  overflow-x: hidden;\r\n`;\r\n\r\nconst Navigation = styled.nav`\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 50;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border-bottom: 1px solid var(--border-light);\r\n  padding: var(--space-4) 0;\r\n`;\r\n\r\nconst NavContainer = styled.div`\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--space-4);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n`;\r\n\r\nconst Logo = styled.div`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  color: var(--text-primary);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n\r\n  &::before {\r\n    content: '🤟';\r\n    font-size: 1.75rem;\r\n  }\r\n`;\r\n\r\nconst BackButton = styled.button`\r\n  background: transparent;\r\n  color: var(--text-secondary);\r\n  border: 1px solid var(--border-medium);\r\n  padding: var(--space-2) var(--space-4);\r\n  border-radius: var(--radius-lg);\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n\r\n  &:hover {\r\n    background: var(--gray-50);\r\n    color: var(--text-primary);\r\n    border-color: var(--border-dark);\r\n  }\r\n`;\r\n\r\nconst PageTitle = styled.h1`\r\n  font-family: var(--font-primary);\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: var(--text-primary);\r\n  text-align: center;\r\n  margin-bottom: var(--space-2);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 2rem;\r\n  }\r\n`;\r\n\r\nconst PageSubtitle = styled.p`\r\n  font-size: 1.125rem;\r\n  color: var(--text-secondary);\r\n  text-align: center;\r\n  margin-bottom: var(--space-12);\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n\r\n  @media (max-width: 768px) {\r\n    margin-bottom: var(--space-10);\r\n  }\r\n`;\r\n\r\nconst TrainingGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: var(--space-8);\r\n  max-width: 1200px;\r\n  margin: 0 auto var(--space-12);\r\n\r\n  @media (max-width: 1024px) {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst CameraSection = styled.div`\r\n  background: var(--bg-primary);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--space-8);\r\n  border: 1px solid var(--border-light);\r\n  box-shadow: var(--shadow-lg);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    box-shadow: var(--shadow-xl);\r\n    border-color: var(--primary-200);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst WebcamContainer = styled.div`\r\n  position: relative;\r\n  border-radius: var(--radius-xl);\r\n  overflow: hidden;\r\n  background: var(--gray-100);\r\n  aspect-ratio: 4/3;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 2px solid var(--border-light);\r\n  margin-bottom: var(--space-4);\r\n`;\r\n\r\nconst StyledWebcam = styled(Webcam)`\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n`;\r\n\r\nconst RecordingOverlay = styled.div`\r\n  position: absolute;\r\n  top: var(--space-4);\r\n  right: var(--space-4);\r\n  background: ${props => props.isRecording ?\r\n    'var(--error-500)' :\r\n    'var(--gray-600)'\r\n  };\r\n  color: white;\r\n  padding: var(--space-2) var(--space-4);\r\n  border-radius: var(--radius-full);\r\n  font-size: 0.875rem;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\r\n\r\n  @keyframes pulse {\r\n    0%, 100% { opacity: 1; }\r\n    50% { opacity: 0.7; }\r\n  }\r\n`;\r\n\r\nconst SignSection = styled.div`\r\n  background: rgba(255, 255, 255, 0.08);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 24px;\r\n  padding: 2rem;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: center;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    border-color: rgba(255, 255, 255, 0.2);\r\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: 1.5rem;\r\n  }\r\n`;\r\n\r\nconst SignTitle = styled.h2`\r\n  font-size: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n  color: #4ECDC4;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n`;\r\n\r\nconst SignDisplay = styled.div`\r\n  width: 280px;\r\n  height: 280px;\r\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 6rem;\r\n  margin-bottom: 1.5rem;\r\n  border: 2px solid rgba(255, 255, 255, 0.1);\r\n  box-shadow: \r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1),\r\n    0 10px 30px rgba(0, 0, 0, 0.2);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.02);\r\n    border-color: rgba(255, 255, 255, 0.2);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 220px;\r\n    height: 220px;\r\n    font-size: 4rem;\r\n  }\r\n`;\r\n\r\nconst SignName = styled.h3`\r\n  font-size: 1.8rem;\r\n  margin-bottom: 1rem;\r\n  color: #FF6B9D;\r\n  font-weight: 700;\r\n  letter-spacing: -0.01em;\r\n`;\r\n\r\nconst SignDescription = styled.p`\r\n  text-align: center;\r\n  line-height: 1.7;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 1rem;\r\n  font-weight: 400;\r\n  max-width: 300px;\r\n`;\r\n\r\nconst ControlsSection = styled.div`\r\n  grid-column: 1 / -1;\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 1.5rem;\r\n  margin-top: 2rem;\r\n  position: relative;\r\n  z-index: 2;\r\n\r\n  @media (max-width: 768px) {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 1rem;\r\n  }\r\n`;\r\n\r\nconst ControlButton = styled.button`\r\n  background: ${props => props.variant === 'primary' \r\n    ? 'linear-gradient(135deg, #FF6B9D 0%, #C44569 100%)' \r\n    : 'rgba(255, 255, 255, 0.1)'};\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid ${props => props.variant === 'primary' \r\n    ? 'rgba(255, 107, 157, 0.3)' \r\n    : 'rgba(255, 255, 255, 0.2)'};\r\n  border-radius: 50px;\r\n  padding: 1rem 2.5rem;\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  min-width: 180px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  box-shadow: ${props => props.variant === 'primary' \r\n    ? '0 10px 30px rgba(255, 107, 157, 0.3)' \r\n    : '0 4px 15px rgba(0, 0, 0, 0.1)'};\r\n\r\n  &:hover {\r\n    transform: translateY(-3px);\r\n    box-shadow: ${props => props.variant === 'primary' \r\n      ? '0 15px 40px rgba(255, 107, 157, 0.4)' \r\n      : '0 8px 25px rgba(0, 0, 0, 0.15)'};\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 100%;\r\n    max-width: 300px;\r\n  }\r\n`;\r\n\r\nconst StatusMessage = styled.div`\r\n  text-align: center;\r\n  margin-top: 1.5rem;\r\n  padding: 1rem 2rem;\r\n  border-radius: 16px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  color: ${props => props.type === 'success' ? '#4ECDC4' : props.type === 'error' ? '#FF6B9D' : 'rgba(255, 255, 255, 0.9)'};\r\n  font-weight: 500;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  position: relative;\r\n  z-index: 2;\r\n`;\r\n\r\nconst RecordingsSection = styled.div`\r\n  margin-top: 3rem;\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 2;\r\n`;\r\n\r\nconst RecordingsTitle = styled.h3`\r\n  color: #4ECDC4;\r\n  margin-bottom: 1.5rem;\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n`;\r\n\r\nconst RecordingsGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 1.5rem;\r\n  max-width: 1000px;\r\n  margin: 0 auto;\r\n\r\n  @media (max-width: 768px) {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n  }\r\n`;\r\n\r\nconst RecordingCard = styled.div`\r\n  background: rgba(255, 255, 255, 0.08);\r\n  backdrop-filter: blur(20px);\r\n  padding: 1.5rem;\r\n  border-radius: 16px;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-4px);\r\n    border-color: rgba(255, 255, 255, 0.2);\r\n    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\r\n  }\r\n`;\r\n\r\nconst RecordingTitle = styled.p`\r\n  margin: 0 0 0.5rem 0;\r\n  color: #FF6B9D;\r\n  font-weight: 600;\r\n  font-size: 1.1rem;\r\n`;\r\n\r\nconst RecordingTime = styled.p`\r\n  margin: 0 0 1rem 0;\r\n  font-size: 0.9rem;\r\n  color: rgba(255, 255, 255, 0.7);\r\n`;\r\n\r\nconst DownloadButton = styled.button`\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 12px;\r\n  padding: 0.6rem 1.2rem;\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  margin: 0 auto;\r\n\r\n  &:hover {\r\n    background: rgba(255, 255, 255, 0.2);\r\n    transform: translateY(-2px);\r\n  }\r\n`;\r\n\r\n// Sample sign language data\r\nconst signLanguageData = [\r\n  {\r\n    name: \"Hello\",\r\n    emoji: \"👋\",\r\n    description: \"Wave your hand from side to side with your palm facing forward\"\r\n  },\r\n  {\r\n    name: \"Thank You\",\r\n    emoji: \"🙏\",\r\n    description: \"Touch your chin with your fingertips and move your hand forward\"\r\n  },\r\n  {\r\n    name: \"Yes\",\r\n    emoji: \"👍\",\r\n    description: \"Make a fist and nod it up and down\"\r\n  },\r\n  {\r\n    name: \"No\",\r\n    emoji: \"👎\",\r\n    description: \"Make a fist and shake it from side to side\"\r\n  },\r\n  {\r\n    name: \"Please\",\r\n    emoji: \"🤲\",\r\n    description: \"Place your open hand on your chest and move it in a circular motion\"\r\n  },\r\n  {\r\n    name: \"Sorry\",\r\n    emoji: \"😔\",\r\n    description: \"Make a fist and rub it in a circular motion on your chest\"\r\n  }\r\n];\r\n\r\nconst TrainingPage = ({ onBackToHome }) => {\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [currentSign, setCurrentSign] = useState(0);\r\n  const [status, setStatus] = useState('');\r\n  const [recordedVideos, setRecordedVideos] = useState([]);\r\n  const webcamRef = useRef(null);\r\n  const mediaRecorderRef = useRef(null);\r\n  const recordedChunksRef = useRef([]);\r\n\r\n  const getRandomSign = useCallback(() => {\r\n    const randomIndex = Math.floor(Math.random() * signLanguageData.length);\r\n    setCurrentSign(randomIndex);\r\n  }, []);\r\n\r\n  const startRecording = useCallback(() => {\r\n    if (!webcamRef.current) {\r\n      setStatus('Camera not available');\r\n      return;\r\n    }\r\n\r\n    mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, {\r\n      mimeType: 'video/webm'\r\n    });\r\n\r\n    mediaRecorderRef.current.ondataavailable = (event) => {\r\n      if (event.data.size > 0) {\r\n        recordedChunksRef.current.push(event.data);\r\n      }\r\n    };\r\n\r\n    mediaRecorderRef.current.onstop = () => {\r\n      const blob = new Blob(recordedChunksRef.current, {\r\n        type: 'video/webm'\r\n      });\r\n      const url = URL.createObjectURL(blob);\r\n      const timestamp = new Date().toISOString();\r\n      \r\n      setRecordedVideos(prev => [...prev, {\r\n        id: timestamp,\r\n        url,\r\n        sign: signLanguageData[currentSign].name,\r\n        timestamp\r\n      }]);\r\n      \r\n      recordedChunksRef.current = [];\r\n      setStatus('Recording saved successfully!');\r\n    };\r\n\r\n    mediaRecorderRef.current.start();\r\n    setIsRecording(true);\r\n    setStatus('Recording started...');\r\n  }, [currentSign]);\r\n\r\n  const stopRecording = useCallback(() => {\r\n    if (mediaRecorderRef.current && isRecording) {\r\n      mediaRecorderRef.current.stop();\r\n      setIsRecording(false);\r\n      setStatus('Processing recording...');\r\n    }\r\n  }, [isRecording]);\r\n\r\n  const downloadRecording = (video) => {\r\n    const a = document.createElement('a');\r\n    a.href = video.url;\r\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\r\n    a.click();\r\n  };\r\n\r\n  React.useEffect(() => {\r\n    getRandomSign();\r\n  }, [getRandomSign]);\r\n\r\n  return (\r\n    <TrainingContainer>\r\n      <Header>\r\n        <BackButton onClick={onBackToHome}>\r\n          ← Back to Home\r\n        </BackButton>\r\n        <Title>Practice Session</Title>\r\n        <div></div>\r\n      </Header>\r\n\r\n      <MainContent>\r\n        <CameraSection>\r\n          <CameraTitle>\r\n            📹 Your Camera\r\n          </CameraTitle>\r\n          <WebcamContainer>\r\n            <StyledWebcam\r\n              ref={webcamRef}\r\n              audio={false}\r\n              screenshotFormat=\"image/jpeg\"\r\n              videoConstraints={{\r\n                width: 640,\r\n                height: 480,\r\n                facingMode: \"user\"\r\n              }}\r\n            />\r\n            <RecordingOverlay isRecording={isRecording}>\r\n              {isRecording ? '🔴 Recording' : '📹 Ready'}\r\n            </RecordingOverlay>\r\n          </WebcamContainer>\r\n        </CameraSection>\r\n\r\n        <SignSection>\r\n          <SignTitle>\r\n            🎯 Practice This Sign\r\n          </SignTitle>\r\n          <SignDisplay>\r\n            {signLanguageData[currentSign].emoji}\r\n          </SignDisplay>\r\n          <SignName>{signLanguageData[currentSign].name}</SignName>\r\n          <SignDescription>\r\n            {signLanguageData[currentSign].description}\r\n          </SignDescription>\r\n        </SignSection>\r\n      </MainContent>\r\n\r\n      <ControlsSection>\r\n        <ControlButton \r\n          variant=\"secondary\" \r\n          onClick={getRandomSign}\r\n          disabled={isRecording}\r\n        >\r\n          🔄 New Sign\r\n        </ControlButton>\r\n        \r\n        <ControlButton \r\n          variant=\"primary\" \r\n          onClick={isRecording ? stopRecording : startRecording}\r\n        >\r\n          {isRecording ? '⏹️ Stop Recording' : '🎬 Start Recording'}\r\n        </ControlButton>\r\n      </ControlsSection>\r\n\r\n      {status && (\r\n        <StatusMessage type={status.includes('error') ? 'error' : status.includes('success') ? 'success' : 'info'}>\r\n          {status}\r\n        </StatusMessage>\r\n      )}\r\n\r\n      {recordedVideos.length > 0 && (\r\n        <RecordingsSection>\r\n          <RecordingsTitle>Your Practice Recordings</RecordingsTitle>\r\n          <RecordingsGrid>\r\n            {recordedVideos.map((video) => (\r\n              <RecordingCard key={video.id}>\r\n                <RecordingTitle>{video.sign}</RecordingTitle>\r\n                <RecordingTime>\r\n                  {new Date(video.timestamp).toLocaleString()}\r\n                </RecordingTime>\r\n                <DownloadButton onClick={() => downloadRecording(video)}>\r\n                  📥 Download\r\n                </DownloadButton>\r\n              </RecordingCard>\r\n            ))}\r\n          </RecordingsGrid>\r\n        </RecordingsSection>\r\n      )}\r\n    </TrainingContainer>\r\n  );\r\n};\r\n\r\nexport default TrainingPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,iBAAiB,GAAGJ,MAAM,CAACK,GAAG;AACpC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,iBAAiB;AAOvB,MAAMG,UAAU,GAAGP,MAAM,CAACQ,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,YAAY,GAAGT,MAAM,CAACK,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMK,IAAI,GAAGV,MAAM,CAACK,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMM,UAAU,GAAGX,MAAM,CAACY,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnBIF,UAAU;AAqBhB,MAAMG,SAAS,GAAGd,MAAM,CAACe,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,YAAY,GAAGhB,MAAM,CAACiB,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,YAAY,GAAGlB,MAAM,CAACK,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMc,aAAa,GAAGnB,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAhBID,aAAa;AAkBnB,MAAME,eAAe,GAAGrB,MAAM,CAACK,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAXID,eAAe;AAarB,MAAME,YAAY,GAAGvB,MAAM,CAACC,MAAM,CAAC;AACnC;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GAJID,YAAY;AAMlB,MAAME,gBAAgB,GAAGzB,MAAM,CAACK,GAAG;AACnC;AACA;AACA;AACA,gBAAgBqB,KAAK,IAAIA,KAAK,CAACC,WAAW,GACtC,kBAAkB,GAClB,iBAAiB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eACeD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,qBAAqB,GAAG,MAAM;AAC1E;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAtBIH,gBAAgB;AAwBtB,MAAMI,WAAW,GAAG7B,MAAM,CAACK,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GApBID,WAAW;AAsBjB,MAAME,SAAS,GAAG/B,MAAM,CAACgC,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GARIF,SAAS;AAUf,MAAMG,WAAW,GAAGlC,MAAM,CAACK,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8B,GAAA,GA1BID,WAAW;AA4BjB,MAAME,QAAQ,GAAGpC,MAAM,CAACqC,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIF,QAAQ;AAQd,MAAMG,eAAe,GAAGvC,MAAM,CAACiB,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GAPID,eAAe;AASrB,MAAME,eAAe,GAAGzC,MAAM,CAACK,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqC,IAAA,GAdID,eAAe;AAgBrB,MAAME,aAAa,GAAG3C,MAAM,CAACY,MAAM;AACnC,gBAAgBc,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,mDAAmD,GACnD,0BAA0B;AAChC;AACA,sBAAsBlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GACpD,0BAA0B,GAC1B,0BAA0B;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,sCAAsC,GACtC,+BAA+B;AACrC;AACA;AACA;AACA,kBAAkBlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,sCAAsC,GACtC,gCAAgC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAzCIF,aAAa;AA2CnB,MAAMG,aAAa,GAAG9C,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,WAAWqB,KAAK,IAAIA,KAAK,CAACqB,IAAI,KAAK,SAAS,GAAG,SAAS,GAAGrB,KAAK,CAACqB,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,0BAA0B;AAC1H;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAZIF,aAAa;AAcnB,MAAMG,iBAAiB,GAAGjD,MAAM,CAACK,GAAG;AACpC;AACA;AACA;AACA;AACA,CAAC;AAAC6C,IAAA,GALID,iBAAiB;AAOvB,MAAME,eAAe,GAAGnD,MAAM,CAACqC,EAAE;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACe,IAAA,GALID,eAAe;AAOrB,MAAME,cAAc,GAAGrD,MAAM,CAACK,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiD,IAAA,GAXID,cAAc;AAapB,MAAME,aAAa,GAAGvD,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmD,IAAA,GAbID,aAAa;AAenB,MAAME,cAAc,GAAGzD,MAAM,CAACiB,CAAC;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACyC,IAAA,GALID,cAAc;AAOpB,MAAME,aAAa,GAAG3D,MAAM,CAACiB,CAAC;AAC9B;AACA;AACA;AACA,CAAC;AAAC2C,IAAA,GAJID,aAAa;AAMnB,MAAME,cAAc,GAAG7D,MAAM,CAACY,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAkD,IAAA,GArBMD,cAAc;AAsBpB,MAAME,gBAAgB,GAAG,CACvB;EACEC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,KAAK;EACXC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAAC1C,WAAW,EAAE2C,cAAc,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0E,WAAW,EAAEC,cAAc,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC4E,MAAM,EAAEC,SAAS,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC8E,cAAc,EAAEC,iBAAiB,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAMgF,SAAS,GAAG/E,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMgF,gBAAgB,GAAGhF,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMiF,iBAAiB,GAAGjF,MAAM,CAAC,EAAE,CAAC;EAEpC,MAAMkF,aAAa,GAAGjF,WAAW,CAAC,MAAM;IACtC,MAAMkF,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGrB,gBAAgB,CAACsB,MAAM,CAAC;IACvEb,cAAc,CAACS,WAAW,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,cAAc,GAAGvF,WAAW,CAAC,MAAM;IACvC,IAAI,CAAC8E,SAAS,CAACU,OAAO,EAAE;MACtBb,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;IAEAI,gBAAgB,CAACS,OAAO,GAAG,IAAIC,aAAa,CAACX,SAAS,CAACU,OAAO,CAACE,MAAM,EAAE;MACrEC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEFZ,gBAAgB,CAACS,OAAO,CAACI,eAAe,GAAIC,KAAK,IAAK;MACpD,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;QACvBf,iBAAiB,CAACQ,OAAO,CAACQ,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;MAC5C;IACF,CAAC;IAEDf,gBAAgB,CAACS,OAAO,CAACS,MAAM,GAAG,MAAM;MACtC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACnB,iBAAiB,CAACQ,OAAO,EAAE;QAC/CxC,IAAI,EAAE;MACR,CAAC,CAAC;MACF,MAAMoD,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACrC,MAAMK,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAE1C5B,iBAAiB,CAAC6B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAClCC,EAAE,EAAEJ,SAAS;QACbH,GAAG;QACHQ,IAAI,EAAE5C,gBAAgB,CAACQ,WAAW,CAAC,CAACP,IAAI;QACxCsC;MACF,CAAC,CAAC,CAAC;MAEHvB,iBAAiB,CAACQ,OAAO,GAAG,EAAE;MAC9Bb,SAAS,CAAC,+BAA+B,CAAC;IAC5C,CAAC;IAEDI,gBAAgB,CAACS,OAAO,CAACqB,KAAK,CAAC,CAAC;IAChCtC,cAAc,CAAC,IAAI,CAAC;IACpBI,SAAS,CAAC,sBAAsB,CAAC;EACnC,CAAC,EAAE,CAACH,WAAW,CAAC,CAAC;EAEjB,MAAMsC,aAAa,GAAG9G,WAAW,CAAC,MAAM;IACtC,IAAI+E,gBAAgB,CAACS,OAAO,IAAI5D,WAAW,EAAE;MAC3CmD,gBAAgB,CAACS,OAAO,CAACuB,IAAI,CAAC,CAAC;MAC/BxC,cAAc,CAAC,KAAK,CAAC;MACrBI,SAAS,CAAC,yBAAyB,CAAC;IACtC;EACF,CAAC,EAAE,CAAC/C,WAAW,CAAC,CAAC;EAEjB,MAAMoF,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGJ,KAAK,CAACb,GAAG;IAClBc,CAAC,CAACI,QAAQ,GAAG,QAAQL,KAAK,CAACL,IAAI,IAAIK,KAAK,CAACV,SAAS,OAAO;IACzDW,CAAC,CAACK,KAAK,CAAC,CAAC;EACX,CAAC;EAED1H,KAAK,CAAC2H,SAAS,CAAC,MAAM;IACpBvC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,oBACE7E,OAAA,CAACC,iBAAiB;IAAAoH,QAAA,gBAChBrH,OAAA,CAACsH,MAAM;MAAAD,QAAA,gBACLrH,OAAA,CAACQ,UAAU;QAAC+G,OAAO,EAAEtD,YAAa;QAAAoD,QAAA,EAAC;MAEnC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb3H,OAAA,CAAC4H,KAAK;QAAAP,QAAA,EAAC;MAAgB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/B3H,OAAA;QAAAwH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAET3H,OAAA,CAAC6H,WAAW;MAAAR,QAAA,gBACVrH,OAAA,CAACgB,aAAa;QAAAqG,QAAA,gBACZrH,OAAA,CAAC8H,WAAW;UAAAT,QAAA,EAAC;QAEb;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACd3H,OAAA,CAACkB,eAAe;UAAAmG,QAAA,gBACdrH,OAAA,CAACoB,YAAY;YACX2G,GAAG,EAAErD,SAAU;YACfsD,KAAK,EAAE,KAAM;YACbC,gBAAgB,EAAC,YAAY;YAC7BC,gBAAgB,EAAE;cAChBC,KAAK,EAAE,GAAG;cACVC,MAAM,EAAE,GAAG;cACXC,UAAU,EAAE;YACd;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF3H,OAAA,CAACsB,gBAAgB;YAACE,WAAW,EAAEA,WAAY;YAAA6F,QAAA,EACxC7F,WAAW,GAAG,cAAc,GAAG;UAAU;YAAAgG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEhB3H,OAAA,CAAC0B,WAAW;QAAA2F,QAAA,gBACVrH,OAAA,CAAC4B,SAAS;UAAAyF,QAAA,EAAC;QAEX;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZ3H,OAAA,CAAC+B,WAAW;UAAAsF,QAAA,EACTzD,gBAAgB,CAACQ,WAAW,CAAC,CAACN;QAAK;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACd3H,OAAA,CAACiC,QAAQ;UAAAoF,QAAA,EAAEzD,gBAAgB,CAACQ,WAAW,CAAC,CAACP;QAAI;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACzD3H,OAAA,CAACoC,eAAe;UAAAiF,QAAA,EACbzD,gBAAgB,CAACQ,WAAW,CAAC,CAACL;QAAW;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEd3H,OAAA,CAACsC,eAAe;MAAA+E,QAAA,gBACdrH,OAAA,CAACwC,aAAa;QACZC,OAAO,EAAC,WAAW;QACnB8E,OAAO,EAAE1C,aAAc;QACvByD,QAAQ,EAAE9G,WAAY;QAAA6F,QAAA,EACvB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAEhB3H,OAAA,CAACwC,aAAa;QACZC,OAAO,EAAC,SAAS;QACjB8E,OAAO,EAAE/F,WAAW,GAAGkF,aAAa,GAAGvB,cAAe;QAAAkC,QAAA,EAErD7F,WAAW,GAAG,mBAAmB,GAAG;MAAoB;QAAAgG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEjBrD,MAAM,iBACLtE,OAAA,CAAC2C,aAAa;MAACC,IAAI,EAAE0B,MAAM,CAACiE,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAGjE,MAAM,CAACiE,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,MAAO;MAAAlB,QAAA,EACvG/C;IAAM;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAChB,EAEAnD,cAAc,CAACU,MAAM,GAAG,CAAC,iBACxBlF,OAAA,CAAC8C,iBAAiB;MAAAuE,QAAA,gBAChBrH,OAAA,CAACgD,eAAe;QAAAqE,QAAA,EAAC;MAAwB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC,eAC3D3H,OAAA,CAACkD,cAAc;QAAAmE,QAAA,EACZ7C,cAAc,CAACgE,GAAG,CAAE3B,KAAK,iBACxB7G,OAAA,CAACoD,aAAa;UAAAiE,QAAA,gBACZrH,OAAA,CAACsD,cAAc;YAAA+D,QAAA,EAAER,KAAK,CAACL;UAAI;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC,eAC7C3H,OAAA,CAACwD,aAAa;YAAA6D,QAAA,EACX,IAAIjB,IAAI,CAACS,KAAK,CAACV,SAAS,CAAC,CAACsC,cAAc,CAAC;UAAC;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eAChB3H,OAAA,CAAC0D,cAAc;YAAC6D,OAAO,EAAEA,CAAA,KAAMX,iBAAiB,CAACC,KAAK,CAAE;YAAAQ,QAAA,EAAC;UAEzD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB,CAAC;QAAA,GAPCd,KAAK,CAACN,EAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQb,CAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACpB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAExB,CAAC;AAACzD,EAAA,CAjKIF,YAAY;AAAA0E,IAAA,GAAZ1E,YAAY;AAmKlB,eAAeA,YAAY;AAAC,IAAA7D,EAAA,EAAAO,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAA+E,IAAA;AAAAC,YAAA,CAAAxI,EAAA;AAAAwI,YAAA,CAAAjI,GAAA;AAAAiI,YAAA,CAAA1H,GAAA;AAAA0H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAAlH,GAAA;AAAAkH,YAAA,CAAAhH,GAAA;AAAAgH,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAApG,IAAA;AAAAoG,YAAA,CAAAjG,IAAA;AAAAiG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA5F,IAAA;AAAA4F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAxF,IAAA;AAAAwF,YAAA,CAAAtF,IAAA;AAAAsF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}