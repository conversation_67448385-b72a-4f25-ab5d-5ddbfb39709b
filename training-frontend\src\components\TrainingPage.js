import React, { useState, useRef, useCallback } from 'react';
import styled from 'styled-components';
import Webcam from 'react-webcam';

const TrainingContainer = styled.div`
  min-height: 100vh;
  padding: 1.5rem;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow-x: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%);
    pointer-events: none;
  }

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 0.8rem 1.5rem;
  color: white;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  @media (max-width: 768px) {
    align-self: flex-start;
  }
`;

const Title = styled.h1`
  font-size: clamp(2rem, 5vw, 3rem);
  margin: 0;
  font-weight: 700;
  background: linear-gradient(135deg, #FF6B9D 0%, #4ECDC4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
`;

const MainContent = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 2;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
`;

const CameraSection = styled.div`
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 768px) {
    padding: 1.5rem;
  }
`;

const CameraTitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #4ECDC4;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const WebcamContainer = styled.div`
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
`;

const StyledWebcam = styled(Webcam)`
  width: 100%;
  height: auto;
  border-radius: 16px;
  display: block;
`;

const RecordingOverlay = styled.div`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: ${props => props.isRecording 
    ? 'linear-gradient(135deg, #FF6B9D, #C44569)' 
    : 'rgba(255, 255, 255, 0.2)'};
  backdrop-filter: blur(10px);
  color: white;
  padding: 0.6rem 1.2rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};
  box-shadow: ${props => props.isRecording 
    ? '0 8px 25px rgba(255, 107, 157, 0.4)' 
    : '0 4px 15px rgba(0, 0, 0, 0.1)'};

  @keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
  }
`;

const SignSection = styled.div`
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 768px) {
    padding: 1.5rem;
  }
`;

const SignTitle = styled.h2`
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #4ECDC4;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const SignDisplay = styled.div`
  width: 280px;
  height: 280px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 6rem;
  margin-bottom: 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.02);
    border-color: rgba(255, 255, 255, 0.2);
  }

  @media (max-width: 768px) {
    width: 220px;
    height: 220px;
    font-size: 4rem;
  }
`;

const SignName = styled.h3`
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: #FF6B9D;
  font-weight: 700;
  letter-spacing: -0.01em;
`;

const SignDescription = styled.p`
  text-align: center;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  font-weight: 400;
  max-width: 300px;
`;

const ControlsSection = styled.div`
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 2rem;
  position: relative;
  z-index: 2;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
`;

const ControlButton = styled.button`
  background: ${props => props.variant === 'primary' 
    ? 'linear-gradient(135deg, #FF6B9D 0%, #C44569 100%)' 
    : 'rgba(255, 255, 255, 0.1)'};
  backdrop-filter: blur(10px);
  border: 1px solid ${props => props.variant === 'primary' 
    ? 'rgba(255, 107, 157, 0.3)' 
    : 'rgba(255, 255, 255, 0.2)'};
  border-radius: 50px;
  padding: 1rem 2.5rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: ${props => props.variant === 'primary' 
    ? '0 10px 30px rgba(255, 107, 157, 0.3)' 
    : '0 4px 15px rgba(0, 0, 0, 0.1)'};

  &:hover {
    transform: translateY(-3px);
    box-shadow: ${props => props.variant === 'primary' 
      ? '0 15px 40px rgba(255, 107, 157, 0.4)' 
      : '0 8px 25px rgba(0, 0, 0, 0.15)'};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  @media (max-width: 768px) {
    width: 100%;
    max-width: 300px;
  }
`;

const StatusMessage = styled.div`
  text-align: center;
  margin-top: 1.5rem;
  padding: 1rem 2rem;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: ${props => props.type === 'success' ? '#4ECDC4' : props.type === 'error' ? '#FF6B9D' : 'rgba(255, 255, 255, 0.9)'};
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 2;
`;

const RecordingsSection = styled.div`
  margin-top: 3rem;
  text-align: center;
  position: relative;
  z-index: 2;
`;

const RecordingsTitle = styled.h3`
  color: #4ECDC4;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
`;

const RecordingsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
`;

const RecordingCard = styled.div`
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  padding: 1.5rem;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  }
`;

const RecordingTitle = styled.p`
  margin: 0 0 0.5rem 0;
  color: #FF6B9D;
  font-weight: 600;
  font-size: 1.1rem;
`;

const RecordingTime = styled.p`
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
`;

const DownloadButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.6rem 1.2rem;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 auto;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }
`;

// Sample sign language data
const signLanguageData = [
  {
    name: "Hello",
    emoji: "👋",
    description: "Wave your hand from side to side with your palm facing forward"
  },
  {
    name: "Thank You",
    emoji: "🙏",
    description: "Touch your chin with your fingertips and move your hand forward"
  },
  {
    name: "Yes",
    emoji: "👍",
    description: "Make a fist and nod it up and down"
  },
  {
    name: "No",
    emoji: "👎",
    description: "Make a fist and shake it from side to side"
  },
  {
    name: "Please",
    emoji: "🤲",
    description: "Place your open hand on your chest and move it in a circular motion"
  },
  {
    name: "Sorry",
    emoji: "😔",
    description: "Make a fist and rub it in a circular motion on your chest"
  }
];

const TrainingPage = ({ onBackToHome }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [currentSign, setCurrentSign] = useState(0);
  const [status, setStatus] = useState('');
  const [recordedVideos, setRecordedVideos] = useState([]);
  const webcamRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const recordedChunksRef = useRef([]);

  const getRandomSign = useCallback(() => {
    const randomIndex = Math.floor(Math.random() * signLanguageData.length);
    setCurrentSign(randomIndex);
  }, []);

  const startRecording = useCallback(() => {
    if (!webcamRef.current) {
      setStatus('Camera not available');
      return;
    }

    mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, {
      mimeType: 'video/webm'
    });

    mediaRecorderRef.current.ondataavailable = (event) => {
      if (event.data.size > 0) {
        recordedChunksRef.current.push(event.data);
      }
    };

    mediaRecorderRef.current.onstop = () => {
      const blob = new Blob(recordedChunksRef.current, {
        type: 'video/webm'
      });
      const url = URL.createObjectURL(blob);
      const timestamp = new Date().toISOString();
      
      setRecordedVideos(prev => [...prev, {
        id: timestamp,
        url,
        sign: signLanguageData[currentSign].name,
        timestamp
      }]);
      
      recordedChunksRef.current = [];
      setStatus('Recording saved successfully!');
    };

    mediaRecorderRef.current.start();
    setIsRecording(true);
    setStatus('Recording started...');
  }, [currentSign]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setStatus('Processing recording...');
    }
  }, [isRecording]);

  const downloadRecording = (video) => {
    const a = document.createElement('a');
    a.href = video.url;
    a.download = `sign_${video.sign}_${video.timestamp}.webm`;
    a.click();
  };

  React.useEffect(() => {
    getRandomSign();
  }, [getRandomSign]);

  return (
    <TrainingContainer>
      <Header>
        <BackButton onClick={onBackToHome}>
          ← Back to Home
        </BackButton>
        <Title>Practice Session</Title>
        <div></div>
      </Header>

      <MainContent>
        <CameraSection>
          <CameraTitle>
            📹 Your Camera
          </CameraTitle>
          <WebcamContainer>
            <StyledWebcam
              ref={webcamRef}
              audio={false}
              screenshotFormat="image/jpeg"
              videoConstraints={{
                width: 640,
                height: 480,
                facingMode: "user"
              }}
            />
            <RecordingOverlay isRecording={isRecording}>
              {isRecording ? '🔴 Recording' : '📹 Ready'}
            </RecordingOverlay>
          </WebcamContainer>
        </CameraSection>

        <SignSection>
          <SignTitle>
            🎯 Practice This Sign
          </SignTitle>
          <SignDisplay>
            {signLanguageData[currentSign].emoji}
          </SignDisplay>
          <SignName>{signLanguageData[currentSign].name}</SignName>
          <SignDescription>
            {signLanguageData[currentSign].description}
          </SignDescription>
        </SignSection>
      </MainContent>

      <ControlsSection>
        <ControlButton 
          variant="secondary" 
          onClick={getRandomSign}
          disabled={isRecording}
        >
          🔄 New Sign
        </ControlButton>
        
        <ControlButton 
          variant="primary" 
          onClick={isRecording ? stopRecording : startRecording}
        >
          {isRecording ? '⏹️ Stop Recording' : '🎬 Start Recording'}
        </ControlButton>
      </ControlsSection>

      {status && (
        <StatusMessage type={status.includes('error') ? 'error' : status.includes('success') ? 'success' : 'info'}>
          {status}
        </StatusMessage>
      )}

      {recordedVideos.length > 0 && (
        <RecordingsSection>
          <RecordingsTitle>Your Practice Recordings</RecordingsTitle>
          <RecordingsGrid>
            {recordedVideos.map((video) => (
              <RecordingCard key={video.id}>
                <RecordingTitle>{video.sign}</RecordingTitle>
                <RecordingTime>
                  {new Date(video.timestamp).toLocaleString()}
                </RecordingTime>
                <DownloadButton onClick={() => downloadRecording(video)}>
                  📥 Download
                </DownloadButton>
              </RecordingCard>
            ))}
          </RecordingsGrid>
        </RecordingsSection>
      )}
    </TrainingContainer>
  );
};

export default TrainingPage; 