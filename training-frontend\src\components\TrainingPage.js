import React, { useState, useRef, useCallback } from 'react';
import styled from 'styled-components';
import Webcam from 'react-webcam';

const TrainingContainer = styled.div`
  min-height: 100vh;
  background: var(--bg-primary);
  position: relative;
  overflow-x: hidden;
`;

const Navigation = styled.nav`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-light);
  padding: var(--space-4) 0;
`;

const NavContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const Logo = styled.div`
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-2);

  &::before {
    content: '🤟';
    font-size: 1.75rem;
  }
`;

const BackButton = styled.button`
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-medium);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);

  &:hover {
    background: var(--gray-50);
    color: var(--text-primary);
    border-color: var(--border-dark);
  }
`;

const PageTitle = styled.h1`
  font-family: var(--font-primary);
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: var(--space-2);

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const PageSubtitle = styled.p`
  font-size: 1.125rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: var(--space-12);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    margin-bottom: var(--space-10);
  }
`;

const TrainingGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
  max-width: 1200px;
  margin: 0 auto var(--space-12);

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
`;

const CameraSection = styled.div`
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
  }

  @media (max-width: 768px) {
    padding: var(--space-6);
  }
`;

const WebcamContainer = styled.div`
  position: relative;
  border-radius: var(--radius-xl);
  overflow: hidden;
  background: var(--gray-100);
  aspect-ratio: 4/3;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--border-light);
  margin-bottom: var(--space-4);
`;

const StyledWebcam = styled(Webcam)`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const RecordingOverlay = styled.div`
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: ${props => props.isRecording ?
    'var(--error-500)' :
    'var(--gray-600)'
  };
  color: white;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }
`;

const SignSection = styled.div`
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
  }

  @media (max-width: 768px) {
    padding: var(--space-6);
  }
`;

const SignDisplay = styled.div`
  width: 200px;
  height: 200px;
  background: var(--primary-50);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  margin-bottom: var(--space-6);
  border: 2px solid var(--primary-200);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.02);
    border-color: var(--primary-300);
    background: var(--primary-100);
  }

  @media (max-width: 768px) {
    width: 150px;
    height: 150px;
    font-size: 3rem;
  }
  }

  @media (max-width: 768px) {
    width: 220px;
    height: 220px;
    font-size: 4rem;
  }
`;

const SignName = styled.h3`
  font-family: var(--font-primary);
  font-size: 1.5rem;
  margin-bottom: var(--space-3);
  color: var(--text-primary);
  font-weight: 700;

  @media (max-width: 768px) {
    font-size: 1.25rem;
  }
`;

const SignDescription = styled.p`
  text-align: center;
  line-height: 1.6;
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 400;
  max-width: 280px;
`;

const ControlsSection = styled.div`
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-top: var(--space-8);

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    gap: var(--space-3);
  }
`;

const ControlButton = styled.button`
  background: ${props => props.variant === 'primary'
    ? 'var(--primary-600)'
    : 'var(--bg-primary)'};
  border: ${props => props.variant === 'primary'
    ? 'none'
    : '1px solid var(--border-medium)'};
  color: ${props => props.variant === 'primary'
    ? 'white'
    : 'var(--text-primary)'};
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.2s ease;
  min-width: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  box-shadow: ${props => props.variant === 'primary'
    ? 'var(--shadow-lg)'
    : 'var(--shadow-sm)'};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.variant === 'primary'
      ? 'var(--shadow-xl)'
      : 'var(--shadow-md)'};
    background: ${props => props.variant === 'primary'
      ? 'var(--primary-700)'
      : 'var(--gray-50)'};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  @media (max-width: 768px) {
    width: 100%;
    max-width: 280px;
  }
`;

const StatusMessage = styled.div`
  text-align: center;
  margin-top: var(--space-6);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  background: ${props =>
    props.type === 'success' ? 'var(--success-500)' :
    props.type === 'error' ? 'var(--error-500)' :
    'var(--primary-600)'
  };
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

const RecordingsSection = styled.div`
  margin-top: var(--space-16);
  background: var(--bg-secondary);
  padding: var(--space-12) var(--space-4);
  border-radius: var(--radius-2xl);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
`;

const RecordingsTitle = styled.h3`
  font-family: var(--font-primary);
  color: var(--text-primary);
  margin-bottom: var(--space-8);
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
`;

const RecordingsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
`;

const RecordingCard = styled.div`
  background: var(--bg-primary);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    border-color: var(--primary-200);
    box-shadow: var(--shadow-lg);
  }
`;

const RecordingTitle = styled.p`
  margin: 0 0 var(--space-2) 0;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1rem;
  font-family: var(--font-primary);
`;

const RecordingTime = styled.p`
  margin: 0 0 var(--space-4) 0;
  font-size: 0.8rem;
  color: var(--text-tertiary);
`;

const DownloadButton = styled.button`
  background: var(--primary-600);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-2) var(--space-4);
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: 0 auto;

  &:hover {
    background: var(--primary-700);
    transform: translateY(-1px);
  }
`;

// Sample sign language data
const signLanguageData = [
  {
    name: "Hello",
    emoji: "👋",
    description: "Wave your hand from side to side with your palm facing forward"
  },
  {
    name: "Thank You",
    emoji: "🙏",
    description: "Touch your chin with your fingertips and move your hand forward"
  },
  {
    name: "Yes",
    emoji: "👍",
    description: "Make a fist and nod it up and down"
  },
  {
    name: "No",
    emoji: "👎",
    description: "Make a fist and shake it from side to side"
  },
  {
    name: "Please",
    emoji: "🤲",
    description: "Place your open hand on your chest and move it in a circular motion"
  },
  {
    name: "Sorry",
    emoji: "😔",
    description: "Make a fist and rub it in a circular motion on your chest"
  }
];

const TrainingPage = ({ onBackToHome }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [currentSign, setCurrentSign] = useState(0);
  const [status, setStatus] = useState('');
  const [recordedVideos, setRecordedVideos] = useState([]);
  const webcamRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const recordedChunksRef = useRef([]);

  const getRandomSign = useCallback(() => {
    const randomIndex = Math.floor(Math.random() * signLanguageData.length);
    setCurrentSign(randomIndex);
  }, []);

  const startRecording = useCallback(() => {
    if (!webcamRef.current) {
      setStatus('Camera not available');
      return;
    }

    mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, {
      mimeType: 'video/webm'
    });

    mediaRecorderRef.current.ondataavailable = (event) => {
      if (event.data.size > 0) {
        recordedChunksRef.current.push(event.data);
      }
    };

    mediaRecorderRef.current.onstop = () => {
      const blob = new Blob(recordedChunksRef.current, {
        type: 'video/webm'
      });
      const url = URL.createObjectURL(blob);
      const timestamp = new Date().toISOString();
      
      setRecordedVideos(prev => [...prev, {
        id: timestamp,
        url,
        sign: signLanguageData[currentSign].name,
        timestamp
      }]);
      
      recordedChunksRef.current = [];
      setStatus('Recording saved successfully!');
    };

    mediaRecorderRef.current.start();
    setIsRecording(true);
    setStatus('Recording started...');
  }, [currentSign]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setStatus('Processing recording...');
    }
  }, [isRecording]);

  const downloadRecording = (video) => {
    const a = document.createElement('a');
    a.href = video.url;
    a.download = `sign_${video.sign}_${video.timestamp}.webm`;
    a.click();
  };

  React.useEffect(() => {
    getRandomSign();
  }, [getRandomSign]);

  return (
    <TrainingContainer>
      <Navigation>
        <NavContainer>
          <Logo>ASL Trainer</Logo>
          <BackButton onClick={onBackToHome}>
            ← Back to Home
          </BackButton>
        </NavContainer>
      </Navigation>

      <MainContent>
        <PageTitle>Practice Session</PageTitle>
        <PageSubtitle>
          Practice sign language with real-time camera feedback and contribute to AI training
        </PageSubtitle>

        <TrainingGrid>
          <CameraSection>
            <SectionTitle>
              📹 Your Camera
            </SectionTitle>
            <WebcamContainer>
              <StyledWebcam
                ref={webcamRef}
                audio={false}
                screenshotFormat="image/jpeg"
                videoConstraints={{
                  width: 640,
                  height: 480,
                  facingMode: "user"
                }}
              />
              <RecordingOverlay isRecording={isRecording}>
                {isRecording ? '🔴 Recording' : '📹 Ready'}
              </RecordingOverlay>
            </WebcamContainer>
          </CameraSection>

          <SignSection>
            <SectionTitle>
              🎯 Practice This Sign
            </SectionTitle>
            <SignDisplay>
              {signLanguageData[currentSign].emoji}
            </SignDisplay>
            <SignName>{signLanguageData[currentSign].name}</SignName>
            <SignDescription>
              {signLanguageData[currentSign].description}
            </SignDescription>
          </SignSection>
        </TrainingGrid>

        <ControlsSection>
          <ControlButton
            variant="secondary"
            onClick={getRandomSign}
            disabled={isRecording}
          >
            🔄 New Sign
          </ControlButton>

          <ControlButton
            variant="primary"
            onClick={isRecording ? stopRecording : startRecording}
          >
            {isRecording ? '⏹️ Stop Recording' : '🎬 Start Recording'}
          </ControlButton>
        </ControlsSection>

        {status && (
          <StatusMessage type={status.includes('error') ? 'error' : status.includes('success') ? 'success' : 'info'}>
            {status}
          </StatusMessage>
        )}

        {recordedVideos.length > 0 && (
          <RecordingsSection>
            <RecordingsTitle>Your Practice Recordings</RecordingsTitle>
            <RecordingsGrid>
              {recordedVideos.map((video) => (
                <RecordingCard key={video.id}>
                  <RecordingTitle>{video.sign}</RecordingTitle>
                  <RecordingTime>
                    {new Date(video.timestamp).toLocaleString()}
                  </RecordingTime>
                  <DownloadButton onClick={() => downloadRecording(video)}>
                    📥 Download
                  </DownloadButton>
                </RecordingCard>
              ))}
            </RecordingsGrid>
          </RecordingsSection>
        )}
      </MainContent>
    </TrainingContainer>
  );
};

export default TrainingPage; 